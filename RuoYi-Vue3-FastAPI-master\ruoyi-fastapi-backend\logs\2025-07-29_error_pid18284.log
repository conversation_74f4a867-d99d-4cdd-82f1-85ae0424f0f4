2025-07-29 09:11:20.463 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-29 09:11:20.464 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-29 09:11:20.491 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-29 09:11:20.491 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-29 09:11:20.492 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-29 09:11:20.525 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-29 09:11:20.555 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-29 09:11:20.556 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-29 09:11:24.859 | 19da056321f3402dbd5e70b2536a57b9 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-29 09:11:24.874 | fe644276dde143dab603579d8f09c009 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-29 09:11:24.993 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-29 09:11:24.993 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 09:11:24.993 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 09:11:24.994 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:11:24.994 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 09:11:24.994 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 09:11:24.994 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 09:11:24.995 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:11:25.000 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 09:11:25.001 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 09:11:25.008 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 09:11:25.008 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 09:11:25.009 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 09:11:25.009 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 09:11:25.011 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 09:11:25.011 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 09:11:25.011 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 09:11:25.011 | ab0ab606fce24c61a46064c908a2b89a | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-29 09:11:28.281 | a7c02249219043c1bdcaf1e6e7fdbf9b | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 09:11:28.281 | a7c02249219043c1bdcaf1e6e7fdbf9b | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 09:11:28.282 | a7c02249219043c1bdcaf1e6e7fdbf9b | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-29 09:11:28.282 | a7c02249219043c1bdcaf1e6e7fdbf9b | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 09:11:28.282 | a7c02249219043c1bdcaf1e6e7fdbf9b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 09:11:28.282 | a7c02249219043c1bdcaf1e6e7fdbf9b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 09:11:28.282 | a7c02249219043c1bdcaf1e6e7fdbf9b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-29 09:11:28.282 | a7c02249219043c1bdcaf1e6e7fdbf9b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 09:11:28.283 | a7c02249219043c1bdcaf1e6e7fdbf9b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 09:11:28.286 | a7c02249219043c1bdcaf1e6e7fdbf9b | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 09:11:28.286 | a7c02249219043c1bdcaf1e6e7fdbf9b | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 09:11:28.286 | a7c02249219043c1bdcaf1e6e7fdbf9b | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 09:11:28.287 | a7c02249219043c1bdcaf1e6e7fdbf9b | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 09:11:28.288 | a7c02249219043c1bdcaf1e6e7fdbf9b | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 09:11:28.288 | a7c02249219043c1bdcaf1e6e7fdbf9b | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 09:11:28.288 | a7c02249219043c1bdcaf1e6e7fdbf9b | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 09:11:28.288 | a7c02249219043c1bdcaf1e6e7fdbf9b | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-29 09:11:28.293 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:52 - 开始WebSocket连接: 任务12 (无认证模式)
2025-07-29 09:11:28.294 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:73 - 客户端 d3a43a5e-9c07-4b0c-b7d9-4484856f58a3 连接到任务 12 的监控流 (无认证模式)
2025-07-29 09:11:31.492 |  | INFO     | module_stream.controller.monitor_websocket_controller:_start_stream_push:180 - 客户端 d3a43a5e-9c07-4b0c-b7d9-4484856f58a3 主动断开连接
2025-07-29 09:11:31.492 |  | INFO     | module_stream.controller.monitor_websocket_controller:_cleanup_connection:209 - 客户端 d3a43a5e-9c07-4b0c-b7d9-4484856f58a3 连接已清理
2025-07-29 09:11:32.009 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-29 09:11:32.010 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 09:11:32.010 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 09:11:32.010 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:11:32.010 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 09:11:32.010 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 09:11:32.011 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 09:11:32.011 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:11:32.011 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 09:11:32.012 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 09:11:32.017 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 09:11:32.017 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 09:11:32.017 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 09:11:32.017 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 09:11:32.018 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 09:11:32.019 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 09:11:32.019 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 09:11:32.019 | 75b6359ffa2a4bfa9ee10d9d9111c271 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-29 09:11:34.294 | fcd811ccba7a4d309feb37e676f7ff93 | INFO     | module_stream.service.task_execution_service:stop_task:306 - 开始停止任务: 12
2025-07-29 09:11:34.296 | fcd811ccba7a4d309feb37e676f7ff93 | INFO     | module_stream.service.task_execution_service:stop_task:332 - 任务 12 不在运行任务列表中
2025-07-29 09:11:34.296 | fcd811ccba7a4d309feb37e676f7ff93 | INFO     | module_stream.service.task_execution_service:stop_task:339 - 任务 12 使用智驱力直接集成，无需清理外部进程
2025-07-29 09:11:34.296 | fcd811ccba7a4d309feb37e676f7ff93 | INFO     | module_stream.service.task_execution_service:stop_task:344 - 监控流停止成功: 12
2025-07-29 09:11:34.296 | fcd811ccba7a4d309feb37e676f7ff93 | INFO     | module_stream.service.task_execution_service:_clear_task_cache:133 - 任务12缓存已清除
2025-07-29 09:11:34.296 | fcd811ccba7a4d309feb37e676f7ff93 | INFO     | module_stream.service.task_execution_service:stop_task:353 - 任务缓存清理成功: 12
2025-07-29 09:11:34.310 | fcd811ccba7a4d309feb37e676f7ff93 | INFO     | module_stream.service.task_execution_service:stop_task:362 - 任务状态更新成功: 12
2025-07-29 09:11:34.310 | fcd811ccba7a4d309feb37e676f7ff93 | INFO     | module_stream.service.task_execution_service:stop_task:374 - 任务 12 停止成功，包括实时监控流
2025-07-29 09:11:34.310 | fcd811ccba7a4d309feb37e676f7ff93 | INFO     | module_stream.controller.monitor_controller:batch_stop_tasks:209 - 批量停止任务成功: [12]
2025-07-29 09:11:34.327 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-29 09:11:34.328 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 09:11:34.328 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 09:11:34.328 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:11:34.328 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 09:11:34.328 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 09:11:34.328 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 09:11:34.328 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:11:34.329 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 09:11:34.329 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 09:11:34.332 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 09:11:34.332 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 09:11:34.332 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 09:11:34.332 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 09:11:34.333 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 09:11:34.334 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 09:11:34.334 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 09:11:34.334 | 34954c9408a442fab1347ccf6bfeaebe | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-29 09:13:14.007 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-29 09:13:14.008 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
