2025-07-29 09:02:42.631 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-29 09:02:42.632 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-29 09:02:42.660 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-29 09:02:42.661 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-29 09:02:42.662 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-29 09:02:42.696 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-29 09:02:42.707 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-29 09:02:42.707 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-29 09:02:44.897 | d2c0250f64074d6e833692565df46431 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-29 09:02:44.912 | 29577bc6d5ed40e586c2c1d5b5da7df6 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-29 09:02:45.023 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-29 09:02:45.023 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 09:02:45.023 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 09:02:45.023 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:02:45.023 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 09:02:45.024 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 09:02:45.024 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 09:02:45.024 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:02:45.024 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 09:02:45.025 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 09:02:45.036 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-29 09:02:45.036 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 09:02:45.036 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-29 09:02:45.037 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-29 09:02:45.039 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 09:02:45.041 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-29 09:02:45.041 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-29 09:02:45.041 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 09:02:45.041 | 5e21d353d33a4ef09c58251c192d6e44 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-29 09:02:48.005 | f56db771ec2242349112fb448e5574cd | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-29 09:02:50.135 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 09:02:50.135 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 09:02:50.135 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-29 09:02:50.135 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 09:02:50.136 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 09:02:50.136 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 09:02:50.136 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-29 09:02:50.137 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 09:02:50.137 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 09:02:50.140 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-29 09:02:50.140 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 09:02:50.141 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-29 09:02:50.141 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-29 09:02:50.142 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 09:02:50.143 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-29 09:02:50.143 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-29 09:02:50.143 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 09:02:50.143 | 2ffded8d01844afd9c4531723311bb64 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-29 09:02:52.142 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-29 09:02:52.142 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 09:02:52.142 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 09:02:52.142 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:02:52.142 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 09:02:52.143 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 09:02:52.143 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 09:02:52.143 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:02:52.143 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 09:02:52.143 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 09:02:52.146 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-29 09:02:52.146 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 09:02:52.146 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-29 09:02:52.146 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-29 09:02:52.148 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 09:02:52.149 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-29 09:02:52.149 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-29 09:02:52.149 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 09:02:52.150 | 6a511613895a419da570f2a3e11e2b95 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-29 09:02:54.937 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:start_task:204 - 开始启动任务: 12
2025-07-29 09:02:54.940 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:498 - 加载算法配置: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info']
2025-07-29 09:02:54.940 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:505 - 加载检测区域配置: ['version', 'created_at', 'detection_areas', 'detection_lines', 'exclusion_areas']
2025-07-29 09:02:54.940 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:512 - 加载告警配置: ['version', 'created_at', 'alert_params']
2025-07-29 09:02:54.940 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:519 - 加载用户配置（优先级最高）: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-29 09:02:54.941 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:525 - 成功加载数据库配置，包含字段: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_params', 'algorithm_id', 'algorithm_name', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-29 09:02:54.941 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:526 - 配置加载优先级: algorithm_config < bbox_config < alert_config < user_config
2025-07-29 09:02:54.941 | e78048b5a0dc495fb156b83b5958dc08 | WARNING  | module_stream.service.task_execution_service:_validate_required_config:640 - 算法 car_counting 缺少配置参数: 置信度阈值 (confidence_threshold 或 conf_thres), NMS阈值 (nms_threshold 或 nms_thres), 输入图像尺寸 (input_size 或 img_size)。将使用默认值，建议在算法配置页面设置这些参数以获得更好的检测效果。
2025-07-29 09:02:54.941 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:664 - 验证模型初始化 - 添加YOLOv5路径: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master
2025-07-29 09:02:54.943 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:679 - 验证模型初始化 - 成功预导入YOLOv5 utils模块
2025-07-29 09:02:54.943 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:684 - 验证模型初始化 - 当前sys.path前5项: ['D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master\\utils', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master', 'D:\\ai-recognition\\RuoYi-Vue3-FastAPI-master\\ruoyi-fastapi-backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs']
2025-07-29 09:02:54.943 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:685 - 验证模型初始化 - 当前工作目录: D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend
2025-07-29 09:02:54.943 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:689 - 验证模型初始化 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-29 09:02:56.248 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:710 - 验证模型初始化 - 成功导入智驱力模型
2025-07-29 09:02:56.249 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-29 09:02:56.249 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-29 09:02:56.249 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-29 09:02:56.249 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 09:02:56.249 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:722 - 验证模型初始化 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 09:02:58.322 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:734 - 验证模型初始化 - 智驱力模型初始化成功
2025-07-29 09:02:58.322 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:735 -    - 设备: cuda
2025-07-29 09:02:58.322 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:736 -    - 图像尺寸: 640
2025-07-29 09:02:58.322 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:737 -    - 置信度阈值: 0.01
2025-07-29 09:02:58.322 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:738 -    - NMS阈值: 0.5
2025-07-29 09:02:58.324 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:751 - 验证模型初始化 - 智驱力后处理器初始化成功
2025-07-29 09:02:58.338 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:start_monitor_stream:4094 - 任务 12 的监控流已启动
2025-07-29 09:02:58.340 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_cache_task_config:112 - 任务12配置已缓存
2025-07-29 09:02:58.340 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:detection_loop:799 - 任务12配置缓存完成: 区域1个, 线段0个
2025-07-29 09:02:58.341 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:detection_loop:829 - 成功预导入YOLOv5 utils模块
2025-07-29 09:02:58.341 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:detection_loop:835 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-29 09:02:58.342 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: zql_detect
2025-07-29 09:02:58.342 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: model
2025-07-29 09:02:58.342 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:detection_loop:855 - 成功导入智驱力模型
2025-07-29 09:02:58.343 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-29 09:02:58.343 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-29 09:02:58.343 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-29 09:02:58.343 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 09:02:58.343 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:detection_loop:867 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 09:02:58.445 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:detection_loop:880 - 智驱力模型初始化成功
2025-07-29 09:02:58.446 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:detection_loop:881 -    - 设备: cuda
2025-07-29 09:02:58.446 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:detection_loop:882 -    - 图像尺寸: 640
2025-07-29 09:02:58.446 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:detection_loop:883 -    - 置信度阈值: 0.01
2025-07-29 09:02:58.446 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:detection_loop:884 -    - NMS阈值: 0.5
2025-07-29 09:02:58.446 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:detection_loop:897 - 智驱力后处理器初始化成功
2025-07-29 09:02:58.512 | e78048b5a0dc495fb156b83b5958dc08 | ERROR    | module_stream.service.task_execution_service:detection_loop:917 - 视频流连接超时或失败: rtsp://127.0.0.1:8554/test1
2025-07-29 09:02:58.514 | e78048b5a0dc495fb156b83b5958dc08 | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3287 - 更新任务状态失败: readexactly() called while another coroutine is already waiting for incoming data
2025-07-29 09:02:58.514 | e78048b5a0dc495fb156b83b5958dc08 | ERROR    | module_stream.service.task_execution_service:detection_loop:1280 - 检测过程中发生错误: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-07-29 09:02:58.515 | e78048b5a0dc495fb156b83b5958dc08 | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3287 - 更新任务状态失败: This session is in 'prepared' state; no further SQL can be emitted within this transaction.
2025-07-29 09:02:58.521 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.service.task_execution_service:start_task:255 - 任务 12 启动成功，包括实时监控流
2025-07-29 09:02:58.521 | e78048b5a0dc495fb156b83b5958dc08 | INFO     | module_stream.controller.monitor_controller:batch_start_tasks:157 - 批量启动任务完全成功: [12]
2025-07-29 09:02:58.532 | 1ace0def3f4d4627b5f5b9ec64750e6b | ERROR    | exceptions.handle:exception_handler:117 - 数据库连接错误: (asyncmy.errors.OperationalError) (2014, 'Command Out of Sync')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-29 09:03:14.008 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-29 09:03:14.008 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
