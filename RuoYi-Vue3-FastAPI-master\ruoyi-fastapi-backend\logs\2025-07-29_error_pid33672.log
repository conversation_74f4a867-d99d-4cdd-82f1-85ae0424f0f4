2025-07-29 09:23:37.021 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-29 09:23:37.022 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-29 09:23:37.053 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-29 09:23:37.053 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-29 09:23:37.054 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-29 09:23:37.090 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-29 09:23:37.100 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-29 09:23:37.101 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-29 09:23:39.608 | 54d40148b9264641ba62b33b0d39eaab | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-29 09:23:39.624 | ab1dcaa974a64de3a09548e2bda56d82 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-29 09:23:39.750 | 8a663b30acd0431cb33030f6a35ccc44 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 09:23:39.750 | 8a663b30acd0431cb33030f6a35ccc44 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 09:23:39.750 | 8a663b30acd0431cb33030f6a35ccc44 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-29 09:23:39.750 | 8a663b30acd0431cb33030f6a35ccc44 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 09:23:39.751 | 8a663b30acd0431cb33030f6a35ccc44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 09:23:39.751 | 8a663b30acd0431cb33030f6a35ccc44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 09:23:39.751 | 8a663b30acd0431cb33030f6a35ccc44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-29 09:23:39.751 | 8a663b30acd0431cb33030f6a35ccc44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 09:23:39.751 | 8a663b30acd0431cb33030f6a35ccc44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 09:23:39.758 | 8a663b30acd0431cb33030f6a35ccc44 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 09:23:39.758 | 8a663b30acd0431cb33030f6a35ccc44 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 09:23:39.758 | 8a663b30acd0431cb33030f6a35ccc44 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 09:23:39.758 | 8a663b30acd0431cb33030f6a35ccc44 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 09:23:39.760 | 8a663b30acd0431cb33030f6a35ccc44 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆监控
2025-07-29 09:23:39.760 | 8a663b30acd0431cb33030f6a35ccc44 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 09:23:39.761 | 8a663b30acd0431cb33030f6a35ccc44 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 09:23:39.761 | 8a663b30acd0431cb33030f6a35ccc44 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-29 09:23:39.768 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:52 - 开始WebSocket连接: 任务13 (无认证模式)
2025-07-29 09:23:39.768 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:73 - 客户端 1ef87516-4b22-43e6-948c-4b3486ad49f4 连接到任务 13 的监控流 (无认证模式)
