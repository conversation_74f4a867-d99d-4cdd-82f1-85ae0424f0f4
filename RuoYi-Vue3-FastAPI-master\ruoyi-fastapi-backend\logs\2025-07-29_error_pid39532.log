2025-07-29 08:58:25.159 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-29 08:58:25.160 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-29 08:58:25.186 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-29 08:58:25.186 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-29 08:58:25.187 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-29 08:58:25.220 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-29 08:58:25.252 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-29 08:58:25.253 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-29 08:58:38.572 | f2e393a7088d46e3882f1bf8df894079 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-29 08:58:38.586 | addf4afdf3ba4d388ae59f4dd80df53f | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-29 08:58:38.722 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-29 08:58:38.723 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 08:58:38.723 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 08:58:38.723 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 08:58:38.723 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 08:58:38.723 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 08:58:38.723 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 08:58:38.723 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 08:58:38.727 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 08:58:38.727 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 08:58:38.735 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 08:58:38.735 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 08:58:38.735 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 08:58:38.735 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 08:58:38.738 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 08:58:38.738 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 08:58:38.738 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 08:58:38.738 | 6b0fefc5ed184eb0918d00c0b77a4bd7 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-29 08:58:43.759 | 482d080039c94010b1c117dc8f0a04f6 | INFO     | module_stream.service.task_execution_service:stop_task:306 - 开始停止任务: 12
2025-07-29 08:58:43.761 | 482d080039c94010b1c117dc8f0a04f6 | INFO     | module_stream.service.task_execution_service:stop_task:332 - 任务 12 不在运行任务列表中
2025-07-29 08:58:43.761 | 482d080039c94010b1c117dc8f0a04f6 | INFO     | module_stream.service.task_execution_service:stop_task:339 - 任务 12 使用智驱力直接集成，无需清理外部进程
2025-07-29 08:58:43.761 | 482d080039c94010b1c117dc8f0a04f6 | INFO     | module_stream.service.task_execution_service:stop_task:344 - 监控流停止成功: 12
2025-07-29 08:58:43.762 | 482d080039c94010b1c117dc8f0a04f6 | INFO     | module_stream.service.task_execution_service:_clear_task_cache:133 - 任务12缓存已清除
2025-07-29 08:58:43.762 | 482d080039c94010b1c117dc8f0a04f6 | INFO     | module_stream.service.task_execution_service:stop_task:353 - 任务缓存清理成功: 12
2025-07-29 08:58:43.774 | 482d080039c94010b1c117dc8f0a04f6 | INFO     | module_stream.service.task_execution_service:stop_task:362 - 任务状态更新成功: 12
2025-07-29 08:58:43.775 | 482d080039c94010b1c117dc8f0a04f6 | INFO     | module_stream.service.task_execution_service:stop_task:374 - 任务 12 停止成功，包括实时监控流
2025-07-29 08:58:43.775 | 482d080039c94010b1c117dc8f0a04f6 | INFO     | module_stream.controller.monitor_controller:batch_stop_tasks:209 - 批量停止任务成功: [12]
2025-07-29 08:58:43.791 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-29 08:58:43.792 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 08:58:43.792 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 08:58:43.792 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 08:58:43.792 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 08:58:43.792 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 08:58:43.792 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 08:58:43.792 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 08:58:43.793 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 08:58:43.793 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 08:58:43.796 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 08:58:43.796 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 08:58:43.797 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 08:58:43.797 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 08:58:43.798 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 08:58:43.798 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 08:58:43.798 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 08:58:43.799 | 68f6ebe91c6244feae4e40eb9fbc2951 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-29 08:58:46.016 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:start_task:204 - 开始启动任务: 12
2025-07-29 08:58:46.018 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:498 - 加载算法配置: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info']
2025-07-29 08:58:46.018 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:505 - 加载检测区域配置: ['version', 'created_at', 'detection_areas', 'detection_lines', 'exclusion_areas']
2025-07-29 08:58:46.018 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:512 - 加载告警配置: ['version', 'created_at', 'alert_params']
2025-07-29 08:58:46.018 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:519 - 加载用户配置（优先级最高）: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-29 08:58:46.019 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:525 - 成功加载数据库配置，包含字段: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_params', 'algorithm_id', 'algorithm_name', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-29 08:58:46.019 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:526 - 配置加载优先级: algorithm_config < bbox_config < alert_config < user_config
2025-07-29 08:58:46.019 | 5c5bcab345d34be68d28e0824abe9b1a | WARNING  | module_stream.service.task_execution_service:_validate_required_config:640 - 算法 car_counting 缺少配置参数: 置信度阈值 (confidence_threshold 或 conf_thres), NMS阈值 (nms_threshold 或 nms_thres), 输入图像尺寸 (input_size 或 img_size)。将使用默认值，建议在算法配置页面设置这些参数以获得更好的检测效果。
2025-07-29 08:58:46.020 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:664 - 验证模型初始化 - 添加YOLOv5路径: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master
2025-07-29 08:58:46.020 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:679 - 验证模型初始化 - 成功预导入YOLOv5 utils模块
2025-07-29 08:58:46.020 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:684 - 验证模型初始化 - 当前sys.path前5项: ['D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master\\utils', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master', 'D:\\ai-recognition\\RuoYi-Vue3-FastAPI-master\\ruoyi-fastapi-backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs']
2025-07-29 08:58:46.021 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:685 - 验证模型初始化 - 当前工作目录: D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend
2025-07-29 08:58:46.021 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:689 - 验证模型初始化 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-29 08:58:47.281 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:710 - 验证模型初始化 - 成功导入智驱力模型
2025-07-29 08:58:47.282 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-29 08:58:47.282 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-29 08:58:47.282 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-29 08:58:47.282 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 08:58:47.282 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:722 - 验证模型初始化 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 08:58:49.526 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:734 - 验证模型初始化 - 智驱力模型初始化成功
2025-07-29 08:58:49.526 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:735 -    - 设备: cuda
2025-07-29 08:58:49.526 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:736 -    - 图像尺寸: 640
2025-07-29 08:58:49.526 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:737 -    - 置信度阈值: 0.01
2025-07-29 08:58:49.527 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:738 -    - NMS阈值: 0.5
2025-07-29 08:58:49.528 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:751 - 验证模型初始化 - 智驱力后处理器初始化成功
2025-07-29 08:58:49.542 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:start_monitor_stream:4094 - 任务 12 的监控流已启动
2025-07-29 08:58:49.543 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_cache_task_config:112 - 任务12配置已缓存
2025-07-29 08:58:49.544 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:detection_loop:799 - 任务12配置缓存完成: 区域1个, 线段0个
2025-07-29 08:58:49.544 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:detection_loop:829 - 成功预导入YOLOv5 utils模块
2025-07-29 08:58:49.545 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:detection_loop:835 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-29 08:58:49.545 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: zql_detect
2025-07-29 08:58:49.546 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: model
2025-07-29 08:58:49.546 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:detection_loop:855 - 成功导入智驱力模型
2025-07-29 08:58:49.546 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-29 08:58:49.546 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-29 08:58:49.547 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-29 08:58:49.547 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 08:58:49.547 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:detection_loop:867 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 08:58:49.670 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:detection_loop:880 - 智驱力模型初始化成功
2025-07-29 08:58:49.671 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:detection_loop:881 -    - 设备: cuda
2025-07-29 08:58:49.671 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:detection_loop:882 -    - 图像尺寸: 640
2025-07-29 08:58:49.671 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:detection_loop:883 -    - 置信度阈值: 0.01
2025-07-29 08:58:49.671 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:detection_loop:884 -    - NMS阈值: 0.5
2025-07-29 08:58:49.671 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:detection_loop:897 - 智驱力后处理器初始化成功
2025-07-29 08:58:49.736 | 5c5bcab345d34be68d28e0824abe9b1a | ERROR    | module_stream.service.task_execution_service:detection_loop:917 - 视频流连接超时或失败: rtsp://127.0.0.1:8554/test1
2025-07-29 08:58:49.739 | 5c5bcab345d34be68d28e0824abe9b1a | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3287 - 更新任务状态失败: readexactly() called while another coroutine is already waiting for incoming data
2025-07-29 08:58:49.739 | 5c5bcab345d34be68d28e0824abe9b1a | ERROR    | module_stream.service.task_execution_service:detection_loop:1280 - 检测过程中发生错误: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-07-29 08:58:49.739 | 5c5bcab345d34be68d28e0824abe9b1a | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3287 - 更新任务状态失败: This session is in 'prepared' state; no further SQL can be emitted within this transaction.
2025-07-29 08:58:49.742 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.service.task_execution_service:start_task:255 - 任务 12 启动成功，包括实时监控流
2025-07-29 08:58:49.742 | 5c5bcab345d34be68d28e0824abe9b1a | INFO     | module_stream.controller.monitor_controller:batch_start_tasks:157 - 批量启动任务完全成功: [12]
2025-07-29 08:58:49.756 | c7c9890b41f34dc8800c5692448c01a6 | ERROR    | exceptions.handle:exception_handler:117 - 数据库连接错误: (asyncmy.errors.OperationalError) (2014, 'Command Out of Sync')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-29 08:58:49.762 | 5d6228492a2b4b9b8f5f9ef818d36d2a | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 08:58:49.763 | 5d6228492a2b4b9b8f5f9ef818d36d2a | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 08:58:49.764 | 5d6228492a2b4b9b8f5f9ef818d36d2a | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-29 08:58:49.764 | 5d6228492a2b4b9b8f5f9ef818d36d2a | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 08:58:49.764 | 5d6228492a2b4b9b8f5f9ef818d36d2a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 08:58:49.765 | 5d6228492a2b4b9b8f5f9ef818d36d2a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 08:58:49.765 | 5d6228492a2b4b9b8f5f9ef818d36d2a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-29 08:58:49.765 | 5d6228492a2b4b9b8f5f9ef818d36d2a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 08:58:49.765 | 5d6228492a2b4b9b8f5f9ef818d36d2a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 08:58:49.768 | 5d6228492a2b4b9b8f5f9ef818d36d2a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 08:58:49.768 | 5d6228492a2b4b9b8f5f9ef818d36d2a | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 08:58:49.768 | 5d6228492a2b4b9b8f5f9ef818d36d2a | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 08:58:49.768 | 5d6228492a2b4b9b8f5f9ef818d36d2a | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 08:58:49.769 | 5d6228492a2b4b9b8f5f9ef818d36d2a | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 08:58:49.770 | 5d6228492a2b4b9b8f5f9ef818d36d2a | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 08:58:49.770 | 5d6228492a2b4b9b8f5f9ef818d36d2a | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 08:58:49.770 | 5d6228492a2b4b9b8f5f9ef818d36d2a | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-29 08:58:49.775 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:52 - 开始WebSocket连接: 任务12 (无认证模式)
2025-07-29 08:58:49.775 |  | INFO     | module_stream.service.task_execution_service:add_monitor_client:4177 - 客户端 f58d9f5e-5d96-444d-b6dc-341b09101659 已连接到任务 12 的监控流
2025-07-29 08:58:49.776 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:73 - 客户端 f58d9f5e-5d96-444d-b6dc-341b09101659 连接到任务 12 的监控流 (无认证模式)
2025-07-29 08:58:52.700 |  | INFO     | module_stream.controller.monitor_websocket_controller:_start_stream_push:180 - 客户端 f58d9f5e-5d96-444d-b6dc-341b09101659 主动断开连接
2025-07-29 08:58:52.700 |  | INFO     | module_stream.service.task_execution_service:remove_monitor_client:4190 - 客户端 f58d9f5e-5d96-444d-b6dc-341b09101659 已断开任务 12 的监控流
2025-07-29 08:58:52.700 |  | INFO     | module_stream.service.task_execution_service:remove_monitor_client:4194 - 任务 12 没有监控客户端，保持监控流运行
2025-07-29 08:58:52.700 |  | INFO     | module_stream.controller.monitor_websocket_controller:_cleanup_connection:209 - 客户端 f58d9f5e-5d96-444d-b6dc-341b09101659 连接已清理
2025-07-29 08:58:52.710 | 3d11743614db47e8947d0f4a0fdb1839 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-29 08:58:52.722 | 27db5b9cb76b4dd79a941a89fe452e54 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-29 08:58:52.805 | 4f6e62ecd4e045dfb6dbe8f569b566c4 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 08:58:52.805 | 4f6e62ecd4e045dfb6dbe8f569b566c4 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 08:58:52.805 | 4f6e62ecd4e045dfb6dbe8f569b566c4 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-29 08:58:52.805 | 4f6e62ecd4e045dfb6dbe8f569b566c4 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 08:58:52.806 | 4f6e62ecd4e045dfb6dbe8f569b566c4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 08:58:52.806 | 4f6e62ecd4e045dfb6dbe8f569b566c4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 08:58:52.806 | 4f6e62ecd4e045dfb6dbe8f569b566c4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-29 08:58:52.806 | 4f6e62ecd4e045dfb6dbe8f569b566c4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 08:58:52.807 | 4f6e62ecd4e045dfb6dbe8f569b566c4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 08:58:52.809 | 4f6e62ecd4e045dfb6dbe8f569b566c4 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 08:58:52.809 | 4f6e62ecd4e045dfb6dbe8f569b566c4 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 08:58:52.810 | 4f6e62ecd4e045dfb6dbe8f569b566c4 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 08:58:52.810 | 4f6e62ecd4e045dfb6dbe8f569b566c4 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 08:58:52.811 | 4f6e62ecd4e045dfb6dbe8f569b566c4 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 08:58:52.811 | 4f6e62ecd4e045dfb6dbe8f569b566c4 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 08:58:52.811 | 4f6e62ecd4e045dfb6dbe8f569b566c4 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 08:58:52.811 | 4f6e62ecd4e045dfb6dbe8f569b566c4 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-29 08:58:52.816 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:52 - 开始WebSocket连接: 任务12 (无认证模式)
2025-07-29 08:58:52.816 |  | INFO     | module_stream.service.task_execution_service:add_monitor_client:4177 - 客户端 9afb0e6a-8dc4-4ddb-ab80-02b1b8cb7745 已连接到任务 12 的监控流
2025-07-29 08:58:52.816 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:73 - 客户端 9afb0e6a-8dc4-4ddb-ab80-02b1b8cb7745 连接到任务 12 的监控流 (无认证模式)
2025-07-29 08:59:01.382 | 767fe7d86ead47759e7c64211314a72e | INFO     | module_stream.service.task_execution_service:stop_task:306 - 开始停止任务: 12
2025-07-29 08:59:01.383 | 767fe7d86ead47759e7c64211314a72e | INFO     | module_stream.service.task_execution_service:stop_task:330 - 已从运行任务列表移除: 12
2025-07-29 08:59:01.384 | 767fe7d86ead47759e7c64211314a72e | INFO     | module_stream.service.task_execution_service:stop_task:339 - 任务 12 使用智驱力直接集成，无需清理外部进程
2025-07-29 08:59:01.384 | 767fe7d86ead47759e7c64211314a72e | INFO     | module_stream.service.task_execution_service:stop_monitor_stream:4118 - 任务 12 的监控流已停止
2025-07-29 08:59:01.384 | 767fe7d86ead47759e7c64211314a72e | INFO     | module_stream.service.task_execution_service:stop_task:344 - 监控流停止成功: 12
2025-07-29 08:59:01.384 | 767fe7d86ead47759e7c64211314a72e | INFO     | module_stream.service.task_execution_service:_clear_task_cache:133 - 任务12缓存已清除
2025-07-29 08:59:01.384 | 767fe7d86ead47759e7c64211314a72e | INFO     | module_stream.service.task_execution_service:stop_task:353 - 任务缓存清理成功: 12
2025-07-29 08:59:01.393 | 767fe7d86ead47759e7c64211314a72e | INFO     | module_stream.service.task_execution_service:stop_task:362 - 任务状态更新成功: 12
2025-07-29 08:59:01.393 | 767fe7d86ead47759e7c64211314a72e | INFO     | module_stream.service.task_execution_service:stop_task:374 - 任务 12 停止成功，包括实时监控流
2025-07-29 08:59:01.408 | 767fe7d86ead47759e7c64211314a72e | INFO     | module_stream.controller.monitor_controller:batch_stop_tasks:209 - 批量停止任务成功: [12]
2025-07-29 08:59:01.450 |  | INFO     | module_stream.controller.monitor_websocket_controller:_start_stream_push:180 - 客户端 9afb0e6a-8dc4-4ddb-ab80-02b1b8cb7745 主动断开连接
2025-07-29 08:59:01.451 |  | INFO     | module_stream.controller.monitor_websocket_controller:_cleanup_connection:209 - 客户端 9afb0e6a-8dc4-4ddb-ab80-02b1b8cb7745 连接已清理
2025-07-29 08:59:04.654 | 3b4f7eb878d14e978764ac02f35c1e97 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-29 08:59:07.751 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-29 08:59:07.751 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 08:59:07.751 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 08:59:07.751 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 08:59:07.751 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 08:59:07.752 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 08:59:07.752 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 08:59:07.753 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 08:59:07.753 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 08:59:07.753 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 08:59:07.757 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 08:59:07.758 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 08:59:07.758 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 08:59:07.758 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 08:59:07.760 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 08:59:07.760 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 08:59:07.760 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 08:59:07.760 | a2461edf5d314176a58ab83e91f63fa9 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-29 08:59:10.360 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:start_task:204 - 开始启动任务: 12
2025-07-29 08:59:10.361 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:498 - 加载算法配置: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info']
2025-07-29 08:59:10.362 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:505 - 加载检测区域配置: ['version', 'created_at', 'detection_areas', 'detection_lines', 'exclusion_areas']
2025-07-29 08:59:10.362 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:512 - 加载告警配置: ['version', 'created_at', 'alert_params']
2025-07-29 08:59:10.362 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:519 - 加载用户配置（优先级最高）: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-29 08:59:10.362 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:525 - 成功加载数据库配置，包含字段: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_params', 'algorithm_id', 'algorithm_name', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-29 08:59:10.362 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:526 - 配置加载优先级: algorithm_config < bbox_config < alert_config < user_config
2025-07-29 08:59:10.362 | 3e41c64a486a413caa5f186d26d0effb | WARNING  | module_stream.service.task_execution_service:_validate_required_config:640 - 算法 car_counting 缺少配置参数: 置信度阈值 (confidence_threshold 或 conf_thres), NMS阈值 (nms_threshold 或 nms_thres), 输入图像尺寸 (input_size 或 img_size)。将使用默认值，建议在算法配置页面设置这些参数以获得更好的检测效果。
2025-07-29 08:59:10.363 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:679 - 验证模型初始化 - 成功预导入YOLOv5 utils模块
2025-07-29 08:59:10.363 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:684 - 验证模型初始化 - 当前sys.path前5项: ['D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master', 'D:\\ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting\\model', 'D:\\ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting\\postprocessor', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting\\postprocessor', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master']
2025-07-29 08:59:10.364 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:685 - 验证模型初始化 - 当前工作目录: D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend
2025-07-29 08:59:10.364 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:689 - 验证模型初始化 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-29 08:59:10.365 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:706 - 验证模型初始化 - 重新加载模块: zql_detect
2025-07-29 08:59:10.366 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:706 - 验证模型初始化 - 重新加载模块: model
2025-07-29 08:59:10.366 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:710 - 验证模型初始化 - 成功导入智驱力模型
2025-07-29 08:59:10.366 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-29 08:59:10.366 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-29 08:59:10.366 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-29 08:59:10.366 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 08:59:10.366 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:722 - 验证模型初始化 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 08:59:10.484 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:734 - 验证模型初始化 - 智驱力模型初始化成功
2025-07-29 08:59:10.484 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:735 -    - 设备: cuda
2025-07-29 08:59:10.485 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:736 -    - 图像尺寸: 640
2025-07-29 08:59:10.485 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:737 -    - 置信度阈值: 0.01
2025-07-29 08:59:10.485 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:738 -    - NMS阈值: 0.5
2025-07-29 08:59:10.485 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:751 - 验证模型初始化 - 智驱力后处理器初始化成功
2025-07-29 08:59:10.497 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:start_monitor_stream:4094 - 任务 12 的监控流已启动
2025-07-29 08:59:10.499 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_cache_task_config:112 - 任务12配置已缓存
2025-07-29 08:59:10.499 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:detection_loop:799 - 任务12配置缓存完成: 区域1个, 线段0个
2025-07-29 08:59:10.499 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:detection_loop:829 - 成功预导入YOLOv5 utils模块
2025-07-29 08:59:10.499 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:detection_loop:835 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-29 08:59:10.500 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: zql_detect
2025-07-29 08:59:10.501 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: model
2025-07-29 08:59:10.501 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:detection_loop:855 - 成功导入智驱力模型
2025-07-29 08:59:10.501 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-29 08:59:10.501 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-29 08:59:10.501 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-29 08:59:10.501 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 08:59:10.502 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:detection_loop:867 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 08:59:10.627 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:detection_loop:880 - 智驱力模型初始化成功
2025-07-29 08:59:10.627 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:detection_loop:881 -    - 设备: cuda
2025-07-29 08:59:10.627 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:detection_loop:882 -    - 图像尺寸: 640
2025-07-29 08:59:10.627 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:detection_loop:883 -    - 置信度阈值: 0.01
2025-07-29 08:59:10.627 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:detection_loop:884 -    - NMS阈值: 0.5
2025-07-29 08:59:10.627 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:detection_loop:897 - 智驱力后处理器初始化成功
2025-07-29 08:59:10.685 | 3e41c64a486a413caa5f186d26d0effb | ERROR    | module_stream.service.task_execution_service:detection_loop:917 - 视频流连接超时或失败: rtsp://127.0.0.1:8554/test1
2025-07-29 08:59:10.687 | 3e41c64a486a413caa5f186d26d0effb | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3287 - 更新任务状态失败: readexactly() called while another coroutine is already waiting for incoming data
2025-07-29 08:59:10.687 | 3e41c64a486a413caa5f186d26d0effb | ERROR    | module_stream.service.task_execution_service:detection_loop:1280 - 检测过程中发生错误: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-07-29 08:59:10.687 | 3e41c64a486a413caa5f186d26d0effb | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3287 - 更新任务状态失败: This session is in 'prepared' state; no further SQL can be emitted within this transaction.
2025-07-29 08:59:10.689 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.service.task_execution_service:start_task:255 - 任务 12 启动成功，包括实时监控流
2025-07-29 08:59:10.689 | 3e41c64a486a413caa5f186d26d0effb | INFO     | module_stream.controller.monitor_controller:batch_start_tasks:157 - 批量启动任务完全成功: [12]
2025-07-29 08:59:10.706 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-29 08:59:10.706 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 08:59:10.706 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 08:59:10.706 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 08:59:10.706 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 08:59:10.706 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 08:59:10.706 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 08:59:10.707 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 08:59:10.707 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 08:59:10.707 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 08:59:10.710 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 08:59:10.710 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 08:59:10.710 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 08:59:10.710 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 08:59:10.711 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 08:59:10.712 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 08:59:10.712 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 08:59:10.712 | 7bc7338eeedd45858008785a57cccfb5 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-29 08:59:16.258 | 07ad4475ef114f8c84c4ec15e53c5855 | ERROR    | exceptions.handle:exception_handler:117 - 数据库连接错误: (asyncmy.errors.OperationalError) (2014, 'Command Out of Sync')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-29 08:59:21.841 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-29 08:59:21.841 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 08:59:21.842 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 08:59:21.842 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 08:59:21.842 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 08:59:21.842 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 08:59:21.843 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 08:59:21.843 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 08:59:21.843 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 08:59:21.844 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 08:59:21.847 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 08:59:21.847 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 08:59:21.847 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 08:59:21.847 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 08:59:21.848 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 08:59:21.849 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 08:59:21.849 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 08:59:21.849 | c1cff2062f32478cb2760562d4e63459 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-29 08:59:23.868 | b0549d99c72d4407a18da11a5f200082 | INFO     | module_stream.service.task_execution_service:stop_task:306 - 开始停止任务: 12
2025-07-29 08:59:23.869 | b0549d99c72d4407a18da11a5f200082 | INFO     | module_stream.service.task_execution_service:stop_task:330 - 已从运行任务列表移除: 12
2025-07-29 08:59:23.870 | b0549d99c72d4407a18da11a5f200082 | INFO     | module_stream.service.task_execution_service:stop_task:339 - 任务 12 使用智驱力直接集成，无需清理外部进程
2025-07-29 08:59:23.870 | b0549d99c72d4407a18da11a5f200082 | INFO     | module_stream.service.task_execution_service:stop_monitor_stream:4118 - 任务 12 的监控流已停止
2025-07-29 08:59:23.870 | b0549d99c72d4407a18da11a5f200082 | INFO     | module_stream.service.task_execution_service:stop_task:344 - 监控流停止成功: 12
2025-07-29 08:59:23.870 | b0549d99c72d4407a18da11a5f200082 | INFO     | module_stream.service.task_execution_service:_clear_task_cache:133 - 任务12缓存已清除
2025-07-29 08:59:23.870 | b0549d99c72d4407a18da11a5f200082 | INFO     | module_stream.service.task_execution_service:stop_task:353 - 任务缓存清理成功: 12
2025-07-29 08:59:23.879 | b0549d99c72d4407a18da11a5f200082 | INFO     | module_stream.service.task_execution_service:stop_task:362 - 任务状态更新成功: 12
2025-07-29 08:59:23.879 | b0549d99c72d4407a18da11a5f200082 | INFO     | module_stream.service.task_execution_service:stop_task:374 - 任务 12 停止成功，包括实时监控流
2025-07-29 08:59:23.892 | b0549d99c72d4407a18da11a5f200082 | INFO     | module_stream.controller.monitor_controller:batch_stop_tasks:209 - 批量停止任务成功: [12]
2025-07-29 08:59:23.909 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-29 08:59:23.909 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 08:59:23.909 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 08:59:23.909 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 08:59:23.910 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 08:59:23.910 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 08:59:23.910 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 08:59:23.910 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 08:59:23.910 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 08:59:23.911 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 08:59:23.913 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 08:59:23.913 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 08:59:23.914 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 08:59:23.914 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 08:59:23.915 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 08:59:23.915 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 08:59:23.915 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 08:59:23.915 | 39ba8fa20ff942a98e84ce6db5af5c8a | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-29 08:59:44.110 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-29 08:59:44.110 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
