"""
任务执行服务
负责启动、停止、暂停检测任务，调用算法包进行检测和告警
"""

import asyncio
import cv2
import json
import os
import sys
import importlib.util
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
import threading
import queue
import time
import numpy as np
from PIL import Image, ImageDraw, ImageFont
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from config.database import async_engine
from exceptions.exception import ServiceException
from module_stream.dao.task_dao import TaskDao
from module_stream.dao.stream_dao import StreamDao
from module_stream.dao.alert_dao import AlertDao
from module_stream.entity.do.task_do import SurveillanceTask
from module_stream.entity.do.stream_do import SurveillanceStream
from module_alert.entity.do.alert_do import SurveillanceAlert
from module_stream.service.algorithm_config_service import AlgorithmConfigService
from utils.log_util import logger



class TaskExecutionService:
    """
    任务执行服务 - 优化版本，参考yolo_ROI_ai.py的高效设计
    """

    # 运行中的任务字典 {task_id: task_process}
    running_tasks: Dict[int, Dict[str, Any]] = {}

    # 算法包基础路径（相对于ai-recognition目录）
    ALGORITHM_BASE_PATH = Path("../../Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo")

    # 实时监控流管理 {task_id: {'frame_queue': queue.Queue, 'clients': set}}
    monitor_streams: Dict[int, Dict] = {}

    # 实时流推送线程
    stream_threads: Dict[int, threading.Thread] = {}

    # ==================== 性能优化缓存 ====================
    # 任务配置缓存 {task_id: {'bbox_config': dict, 'alert_config': dict, 'algorithm_config': dict}}
    task_config_cache: Dict[int, Dict[str, Any]] = {}

    # 模型实例缓存 {task_id: {'model': model_instance, 'postprocessor': postprocessor_instance}}
    model_cache: Dict[int, Dict[str, Any]] = {}

    # 帧缓冲队列大小（参考yolo_ROI_ai.py的队列设计）
    FRAME_BUFFER_SIZE = 5

    # 车辆跟踪缓存 {task_id: {'vehicles': {track_id: {'last_seen': timestamp, 'bbox': [x1,y1,x2,y2], 'center': [x,y]}}, 'next_id': int}}
    vehicle_tracking_cache: Dict[int, Dict[str, Any]] = {}

    @classmethod
    def _cache_task_config(cls, task_info: SurveillanceTask) -> Dict[str, Any]:
        """
        缓存任务配置，避免每帧重复解析JSON
        参考yolo_ROI_ai.py的配置缓存设计
        """
        task_id = task_info.task_id

        # 检查缓存是否存在
        if task_id in cls.task_config_cache:
            return cls.task_config_cache[task_id]

        try:
            # 解析algorithm_config
            if isinstance(task_info.algorithm_config, dict):
                algorithm_config = task_info.algorithm_config
            elif isinstance(task_info.algorithm_config, str):
                algorithm_config = json.loads(task_info.algorithm_config) if task_info.algorithm_config else {}
            else:
                algorithm_config = {}

            # 解析bbox_config
            if isinstance(task_info.bbox_config, dict):
                bbox_config = task_info.bbox_config
            elif isinstance(task_info.bbox_config, str):
                bbox_config = json.loads(task_info.bbox_config) if task_info.bbox_config else {}
            else:
                bbox_config = {}

            # 解析alert_config
            if isinstance(task_info.alert_config, dict):
                alert_config = task_info.alert_config
            elif isinstance(task_info.alert_config, str):
                alert_config = json.loads(task_info.alert_config) if task_info.alert_config else {}
            else:
                alert_config = {}

            # 缓存配置
            cached_config = {
                'algorithm_config': algorithm_config,
                'bbox_config': bbox_config,
                'alert_config': alert_config,
                'algorithm_id': task_info.algorithm_id,
                'algorithm_type': task_info.algorithm_type
            }

            cls.task_config_cache[task_id] = cached_config
            logger.info(f"任务{task_id}配置已缓存")
            return cached_config

        except Exception as e:
            logger.error(f"缓存任务配置失败: {e}")
            # 返回默认配置
            return {
                'algorithm_config': {},
                'bbox_config': {},
                'alert_config': {},
                'algorithm_id': task_info.algorithm_id,
                'algorithm_type': task_info.algorithm_type
            }

    @classmethod
    def _clear_task_cache(cls, task_id: int):
        """清除任务缓存"""
        if task_id in cls.task_config_cache:
            del cls.task_config_cache[task_id]
        if task_id in cls.model_cache:
            del cls.model_cache[task_id]
        logger.info(f"任务{task_id}缓存已清除")

    @classmethod
    def _put_chinese_text(cls, img, text, position, font_size=20, color=(255, 255, 255)):
        """
        在图像上绘制中文文字
        Args:
            img: OpenCV图像
            text: 要绘制的文字
            position: 文字位置 (x, y)
            font_size: 字体大小
            color: 文字颜色 (B, G, R)
        Returns:
            绘制后的图像
        """
        try:
            # 转换为PIL图像
            img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(img_pil)

            # 尝试使用系统中文字体
            font_paths = [
                "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
                "C:/Windows/Fonts/simhei.ttf",  # 黑体
                "C:/Windows/Fonts/simsun.ttc",  # 宋体
                "/System/Library/Fonts/PingFang.ttc",  # macOS
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
            ]

            font = None
            for font_path in font_paths:
                if os.path.exists(font_path):
                    try:
                        font = ImageFont.truetype(font_path, font_size)
                        break
                    except:
                        continue

            if font is None:
                # 使用默认字体
                font = ImageFont.load_default()

            # 转换颜色格式 (BGR -> RGB)
            color_rgb = (color[2], color[1], color[0])

            # 绘制文字
            draw.text(position, text, font=font, fill=color_rgb)

            # 转换回OpenCV格式
            img_cv = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
            return img_cv

        except Exception as e:
            # 如果出错，使用英文替代
            logger.warning(f"中文字体绘制失败: {e}，使用英文替代")
            # 将中文替换为英文
            text_en = text.replace("人", "person").replace("小轿车", "car").replace("货车", "truck").replace("公交车", "bus")
            text_en = text_en.replace("告警", "alert").replace("排除", "exclude").replace("区域", "area").replace("线段", "line")
            cv2.putText(img, text_en, position, cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            return img
    
    @classmethod
    async def start_task(cls, query_db: AsyncSession, task_id: int, user_id: int) -> bool:
        """
        启动检测任务

        :param query_db: 数据库会话
        :param task_id: 任务ID
        :param user_id: 用户ID
        :return: 启动结果
        """
        logger.info(f"开始启动任务: {task_id}")

        try:
            # 1. 验证任务权限和状态
            task_info = await cls._validate_task_permission(query_db, task_id, user_id)
            if not task_info:
                raise ServiceException(message='任务不存在或无权限访问')

            if task_info.status == '1':
                raise ServiceException(message='任务已在运行中')

            # 检查是否已经在运行任务列表中
            if task_id in cls.running_tasks:
                logger.warning(f"任务 {task_id} 已在运行列表中，先清理")
                await cls._cleanup_task_resources(task_id)

            # 2. 获取视频流信息
            stream_info = await StreamDao.get_stream_detail_by_id(query_db, task_info.stream_id)
            if not stream_info:
                raise ServiceException(message='关联的视频流不存在')

            # 3. 加载算法配置
            algorithm_config = await cls._load_algorithm_config(task_info)
            if not algorithm_config:
                raise ServiceException(message='无法加载算法配置')

            # 3.1 验证配置参数（给出警告但不阻止启动）
            cls._validate_required_config(algorithm_config, task_info.algorithm_id)

            # 4. 启动检测任务
            task_process = await cls._start_detection_process(
                task_info, stream_info, algorithm_config, query_db
            )

            if not task_process:
                raise ServiceException(message='检测进程启动失败')

            # 5. 启动实时监控流
            cls.start_monitor_stream(task_id)

            # 6. 记录运行中的任务
            cls.running_tasks[task_id] = {
                'process': task_process,
                'start_time': datetime.now(),
                'task_info': task_info,
                'stream_info': stream_info
            }

            # 7. 更新任务状态（放在最后，确保前面的步骤都成功）
            await cls._update_task_status(query_db, task_id, '1')

            logger.info(f"任务 {task_id} 启动成功，包括实时监控流")
            return True

        except Exception as e:
            error_str = str(e)
            logger.error(f"启动任务失败: {task_id}, 错误: {e}")

            # 检查是否是数据库连接同步错误
            if "Command Out of Sync" in error_str or "OperationalError" in error_str:
                logger.error(f"检测到数据库连接同步错误，立即停止任务启动: {task_id}")

                # 立即停止任务启动过程
                try:
                    # 强制清理所有相关资源
                    await cls._emergency_cleanup_task(task_id)

                    # 回滚数据库事务
                    try:
                        await query_db.rollback()
                        logger.info(f"数据库事务已回滚: {task_id}")
                    except Exception as rollback_error:
                        logger.error(f"数据库回滚失败: {rollback_error}")

                    # 重新创建数据库会话并更新状态
                    await cls._force_update_task_status_to_stopped(task_id)

                except Exception as emergency_error:
                    logger.error(f"紧急清理失败: {task_id}, 错误: {emergency_error}")

                raise ServiceException(message=f'数据库连接同步错误，任务启动已停止: {task_id}')

            # 启动失败时清理资源
            try:
                await cls._cleanup_task_resources(task_id)
                # 确保数据库状态为停止
                await cls._update_task_status(query_db, task_id, '0')
            except Exception as cleanup_error:
                logger.error(f"清理任务资源失败: {task_id}, 错误: {cleanup_error}")

            raise ServiceException(message=f'启动任务失败: {str(e)}')
    
    @classmethod
    async def stop_task(cls, query_db: AsyncSession, task_id: int, user_id: int) -> bool:
        """
        停止检测任务

        :param query_db: 数据库会话
        :param task_id: 任务ID
        :param user_id: 用户ID
        :return: 停止结果
        """
        logger.info(f"开始停止任务: {task_id}")

        # 记录停止过程中的错误，但不中断停止流程
        errors = []

        try:
            # 1. 验证任务权限
            task_info = await cls._validate_task_permission(query_db, task_id, user_id)
            if not task_info:
                raise ServiceException(message='任务不存在或无权限访问')

            # 2. 停止检测进程（异步任务）
            try:
                if task_id in cls.running_tasks:
                    task_process = cls.running_tasks[task_id]['process']
                    if task_process and not task_process.done():
                        logger.info(f"取消异步任务: {task_id}")
                        task_process.cancel()
                        # 等待任务真正结束
                        try:
                            await asyncio.wait_for(task_process, timeout=5.0)
                        except (asyncio.CancelledError, asyncio.TimeoutError):
                            logger.info(f"异步任务已取消: {task_id}")
                    del cls.running_tasks[task_id]
                    logger.info(f"已从运行任务列表移除: {task_id}")
                else:
                    logger.info(f"任务 {task_id} 不在运行任务列表中")
            except Exception as e:
                error_msg = f"停止异步任务失败: {e}"
                logger.error(error_msg)
                errors.append(error_msg)

            # 3. 清理其他资源（原算法执行服务已移除，因为使用智驱力直接集成）
            logger.info(f"任务 {task_id} 使用智驱力直接集成，无需清理外部进程")

            # 4. 停止实时监控流
            try:
                cls.stop_monitor_stream(task_id)
                logger.info(f"监控流停止成功: {task_id}")
            except Exception as e:
                error_msg = f"停止监控流失败: {e}"
                logger.error(error_msg)
                errors.append(error_msg)

            # 5. 清理任务缓存（性能优化）
            try:
                cls._clear_task_cache(task_id)
                logger.info(f"任务缓存清理成功: {task_id}")
            except Exception as e:
                error_msg = f"清理任务缓存失败: {e}"
                logger.error(error_msg)
                errors.append(error_msg)

            # 5. 更新任务状态（这个必须成功）
            try:
                await cls._update_task_status(query_db, task_id, '0')
                logger.info(f"任务状态更新成功: {task_id}")
            except Exception as e:
                error_msg = f"更新任务状态失败: {e}"
                logger.error(error_msg)
                errors.append(error_msg)
                # 状态更新失败是严重错误，需要抛出异常
                raise ServiceException(message=f'更新任务状态失败: {str(e)}')

            # 6. 记录停止结果
            if errors:
                logger.warning(f"任务 {task_id} 停止完成，但有部分错误: {'; '.join(errors)}")
            else:
                logger.info(f"任务 {task_id} 停止成功，包括实时监控流")

            return True

        except ServiceException:
            # 重新抛出业务异常
            raise
        except Exception as e:
            logger.error(f"停止任务失败: {task_id}, 错误: {e}")
            raise ServiceException(message=f'停止任务失败: {str(e)}')
    
    @classmethod
    async def pause_task(cls, query_db: AsyncSession, task_id: int, user_id: int) -> bool:
        """
        暂停检测任务
        
        :param query_db: 数据库会话
        :param task_id: 任务ID
        :param user_id: 用户ID
        :return: 暂停结果
        """
        try:
            # 1. 验证任务权限
            task_info = await cls._validate_task_permission(query_db, task_id, user_id)
            if not task_info:
                raise ServiceException(message='任务不存在或无权限访问')
            
            if task_info.status != '1':
                raise ServiceException(message='任务未在运行中')
            
            # 2. 暂停检测进程（这里简化为停止，实际可以实现更复杂的暂停逻辑）
            if task_id in cls.running_tasks:
                task_process = cls.running_tasks[task_id]['process']
                if task_process and not task_process.done():
                    task_process.cancel()
                del cls.running_tasks[task_id]
            
            # 3. 更新任务状态
            await cls._update_task_status(query_db, task_id, '2')
            
            logger.info(f"任务 {task_id} 暂停成功")
            return True
            
        except Exception as e:
            logger.error(f"暂停任务失败: {task_id}, 错误: {e}")
            raise ServiceException(message=f'暂停任务失败: {str(e)}')
    
    @classmethod
    async def _validate_task_permission(cls, query_db: AsyncSession, task_id: int, user_id: int) -> Optional[SurveillanceTask]:
        """
        验证任务权限（移除del_flag过滤，使用物理删除）
        """
        query = (
            select(SurveillanceTask)
            .join(SurveillanceStream, SurveillanceTask.stream_id == SurveillanceStream.stream_id)
            .where(
                SurveillanceTask.task_id == task_id,
                SurveillanceStream.user_id == user_id
            )
        )
        result = await query_db.execute(query)
        return result.scalar_one_or_none()
    
    @classmethod
    def _extract_model_parameters_from_config(cls, algorithm_config: Dict[str, Any], default_config: Dict[str, Any], task_info=None) -> Dict[str, Any]:
        """
        从算法配置中提取模型参数，支持多种配置格式，确保user_config优先级最高

        :param algorithm_config: 算法配置字典（已经合并了所有配置，user_config优先级最高）
        :param default_config: 默认配置
        :param task_info: 任务信息，包含user_config
        :return: 提取后的模型配置
        """
        model_config = default_config.copy()

        try:
            # 使用统一的优先级获取方法来提取模型参数
            # algorithm_config已经按优先级合并了所有配置（user_config优先级最高）

            # 提取置信度阈值 - 必需参数，不提供默认值
            conf_thres = cls._get_config_value_with_priority(
                algorithm_config,
                ['confidence_threshold', 'conf_thres'],  # 按优先级排序
                required=True
            )
            model_config['conf_thres'] = conf_thres

            # 提取NMS阈值 - 必需参数，不提供默认值
            nms_thres = cls._get_config_value_with_priority(
                algorithm_config,
                ['nms_threshold', 'nms_thres'],  # 按优先级排序
                required=True
            )
            model_config['nms_thres'] = nms_thres

            # 提取图像尺寸 - 可选参数，提供默认值
            img_size = cls._get_config_value_with_priority(
                algorithm_config,
                ['input_size', 'img_size'],  # 按优先级排序
                default_value=640,
                required=False
            )
            model_config['img_size'] = img_size

            logger.info(f"最终模型配置: {model_config}")

        except Exception as e:
            logger.error(f"提取模型参数时发生错误: {e}")
            logger.error(f"算法配置内容: {algorithm_config}")

        return model_config

    @classmethod
    async def _load_algorithm_config(cls, task_info: SurveillanceTask) -> Optional[Dict[str, Any]]:
        """
        从数据库加载算法配置，合并多个配置字段，确保user_config优先级最高
        """
        try:
            config = {}

            # 1. 先加载算法配置（algorithm_config）作为基础配置
            if task_info.algorithm_config:
                algorithm_config = task_info.algorithm_config if isinstance(task_info.algorithm_config, dict) else json.loads(task_info.algorithm_config)
                config.update(algorithm_config)
                logger.info(f"加载算法配置: {list(algorithm_config.keys())}")

            # 2. 加载检测区域配置（bbox_config）
            if task_info.bbox_config:
                bbox_config = task_info.bbox_config if isinstance(task_info.bbox_config, dict) else json.loads(task_info.bbox_config)
                # 使用深度合并，避免完全覆盖
                config = cls._deep_merge_config(config, bbox_config)
                logger.info(f"加载检测区域配置: {list(bbox_config.keys())}")

            # 3. 加载告警配置（alert_config）
            if task_info.alert_config:
                alert_config = task_info.alert_config if isinstance(task_info.alert_config, dict) else json.loads(task_info.alert_config)
                # 使用深度合并，避免完全覆盖
                config = cls._deep_merge_config(config, alert_config)
                logger.info(f"加载告警配置: {list(alert_config.keys())}")

            # 4. 最后加载用户配置（user_config），确保优先级最高
            if task_info.user_config:
                user_config = task_info.user_config if isinstance(task_info.user_config, dict) else json.loads(task_info.user_config)
                # 使用深度合并，确保用户配置覆盖其他配置
                config = cls._deep_merge_config(config, user_config)
                logger.info(f"加载用户配置（优先级最高）: {list(user_config.keys())}")

            if not config:
                logger.warning(f"数据库中无任何配置信息: {task_info.algorithm_id}")
                return None

            logger.info(f"成功加载数据库配置，包含字段: {list(config.keys())}")
            logger.info(f"配置加载优先级: algorithm_config < bbox_config < alert_config < user_config")
            return config

        except Exception as e:
            logger.error(f"加载算法配置失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    @classmethod
    def _deep_merge_config(cls, base_config: Dict[str, Any], user_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        深度合并配置，user_config优先级更高
        """
        result = base_config.copy()
        for key, value in user_config.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = cls._deep_merge_config(result[key], value)
            else:
                result[key] = value
        return result

    @classmethod
    def _get_config_value_with_priority(cls, config: Dict[str, Any], field_names: List[str], default_value=None, required=False):
        """
        按优先级从配置中获取值，支持多个字段名
        支持从model_parameters、model_params等嵌套字段中获取参数

        :param config: 配置字典
        :param field_names: 字段名列表，按优先级从低到高排序
        :param default_value: 默认值
        :param required: 是否为必需参数，如果为True且未找到配置则抛出异常
        :return: 找到的值或默认值
        """
        value = default_value
        found_field = None
        found_source = None

        # 1. 从顶层配置获取值
        for field_name in field_names:
            if field_name in config:
                value = config[field_name]
                found_field = field_name
                found_source = "顶层配置"
                # 不要break，让后面的字段覆盖前面的（优先级更高）

        # 2. 从model_parameters字段中获取值（优先级更高）
        if 'model_parameters' in config and isinstance(config['model_parameters'], dict):
            model_params = config['model_parameters']
            for field_name in field_names:
                if field_name in model_params:
                    value = model_params[field_name]
                    found_field = field_name
                    found_source = "model_parameters"

        # 3. 从model_params字段中获取值（优先级最高）
        if 'model_params' in config and isinstance(config['model_params'], dict):
            model_params = config['model_params']
            for field_name in field_names:
                if field_name in model_params:
                    value = model_params[field_name]
                    found_field = field_name
                    found_source = "model_params"

        if found_field:
            logger.info(f"从{found_source}获取 {found_field}: {value}")
        else:
            if required:
                # 如果是必需参数，无论是否有默认值都抛出异常，让前端知道缺失参数
                raise ValueError(f"缺少必需的配置参数: {field_names}。请在算法配置中设置这些参数。")
            logger.info(f"未找到配置参数 {field_names}，使用默认值: {default_value}")

        return value

    @classmethod
    def _validate_required_config(cls, algorithm_config: Dict[str, Any], algorithm_id: str) -> None:
        """
        验证必需的配置参数是否存在
        如果缺少关键参数，给出警告但允许使用默认值

        :param algorithm_config: 算法配置字典
        :param algorithm_id: 算法ID
        """
        missing_params = []

        # 检查置信度阈值
        conf_found = False
        for field_name in ['confidence_threshold', 'conf_thres']:
            if field_name in algorithm_config:
                conf_found = True
                break
        if not conf_found:
            missing_params.append('置信度阈值 (confidence_threshold 或 conf_thres)')

        # 检查NMS阈值
        nms_found = False
        for field_name in ['nms_threshold', 'nms_thres']:
            if field_name in algorithm_config:
                nms_found = True
                break
        if not nms_found:
            missing_params.append('NMS阈值 (nms_threshold 或 nms_thres)')

        # 检查输入尺寸
        size_found = False
        for field_name in ['input_size', 'img_size']:
            if field_name in algorithm_config:
                size_found = True
                break
        if not size_found:
            missing_params.append('输入图像尺寸 (input_size 或 img_size)')

        if missing_params:
            warning_msg = f"算法 {algorithm_id} 缺少配置参数: {', '.join(missing_params)}。将使用默认值，建议在算法配置页面设置这些参数以获得更好的检测效果。"
            logger.warning(warning_msg)

    @classmethod
    async def _validate_model_initialization(
        cls,
        task_info: SurveillanceTask,
        algorithm_config: Dict[str, Any]
    ) -> None:
        """
        验证模型是否能正确初始化
        如果初始化失败，抛出异常
        """
        algorithm_base_path = "D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968"
        algorithm_dir = f"{algorithm_base_path}/{task_info.algorithm_id}"

        # 保存当前工作目录
        original_cwd = os.getcwd()

        try:
            # 在切换目录之前，先确保YOLOv5路径在sys.path中
            import sys
            yolov5_absolute_path = "D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master"
            if os.path.exists(yolov5_absolute_path) and yolov5_absolute_path not in sys.path:
                sys.path.insert(0, yolov5_absolute_path)
                logger.info(f"验证模型初始化 - 添加YOLOv5路径: {yolov5_absolute_path}")

            # 预先导入YOLOv5的utils模块，解决TryExcept导入问题
            try:
                utils_path = os.path.join(yolov5_absolute_path, "utils")
                if utils_path not in sys.path:
                    sys.path.insert(0, utils_path)

                # 手动导入utils模块并注册到sys.modules
                import importlib.util
                utils_init_path = os.path.join(utils_path, "__init__.py")
                spec = importlib.util.spec_from_file_location("utils", utils_init_path)
                utils_module = importlib.util.module_from_spec(spec)
                sys.modules["utils"] = utils_module
                spec.loader.exec_module(utils_module)
                logger.info("验证模型初始化 - 成功预导入YOLOv5 utils模块")
            except Exception as e:
                logger.warning(f"验证模型初始化 - 预导入YOLOv5 utils模块失败: {e}")

            # 调试信息：打印当前sys.path
            logger.info(f"验证模型初始化 - 当前sys.path前5项: {sys.path[:5]}")
            logger.info(f"验证模型初始化 - 当前工作目录: {os.getcwd()}")

            # 切换到算法包目录
            os.chdir(algorithm_dir)
            logger.info(f"验证模型初始化 - 切换到算法包目录: {algorithm_dir}")

            # 添加算法包路径到sys.path
            if algorithm_dir not in sys.path:
                sys.path.insert(0, algorithm_dir)

            # 添加model目录到sys.path
            model_dir = os.path.join(algorithm_dir, "model")
            if model_dir not in sys.path:
                sys.path.insert(0, model_dir)

            # 强制清除模块缓存，确保使用最新代码
            import importlib
            module_names_to_reload = ['zql_detect', 'model']
            for module_name in module_names_to_reload:
                if module_name in sys.modules:
                    importlib.reload(sys.modules[module_name])
                    logger.info(f"验证模型初始化 - 重新加载模块: {module_name}")

            # 直接导入模型
            from zql_detect import Model
            logger.info("验证模型初始化 - 成功导入智驱力模型")

            # 模型参数配置，从数据库获取
            # 默认模型参数：如果数据库中没有配置则使用这些默认值
            model_config = {
                'img_size': 640,        # 输入图像尺寸
                'conf_thres': 0.25,     # 置信度阈值
                'nms_thres': 0.45       # NMS阈值
            }

            # 从数据库配置中提取模型参数
            model_config = cls._extract_model_parameters_from_config(algorithm_config, model_config)
            logger.info(f"验证模型初始化 - 最终模型配置: {model_config}")

            # 创建模型实例（完全模仿测试代码）
            model = Model(
                acc_id=0,
                name=task_info.algorithm_id,
                conf=model_config
            )

            if not model.status:
                raise RuntimeError(f"智驱力模型初始化失败: {task_info.algorithm_id}")

            logger.info(f"验证模型初始化 - 智驱力模型初始化成功")
            logger.info(f"   - 设备: {getattr(model, 'device', 'unknown')}")
            logger.info(f"   - 图像尺寸: {getattr(model, 'img_size', 'unknown')}")
            logger.info(f"   - 置信度阈值: {getattr(model, 'conf_thres', 'unknown')}")
            logger.info(f"   - NMS阈值: {getattr(model, 'nms_thres', 'unknown')}")

            # 验证后处理器
            postprocessor_dir = os.path.join(algorithm_dir, "postprocessor")
            if postprocessor_dir not in sys.path:
                sys.path.insert(0, postprocessor_dir)

            # 动态导入后处理器模块
            postprocessor_module_name = task_info.algorithm_id
            postprocessor_module = __import__(postprocessor_module_name)

            # 创建后处理器实例
            postprocessor = postprocessor_module.Postprocessor(0, task_info.algorithm_id)
            logger.info("验证模型初始化 - 智驱力后处理器初始化成功")

        except Exception as e:
            logger.error(f"模型初始化验证失败: {e}")
            import traceback
            traceback.print_exc()
            raise RuntimeError(f"模型初始化验证失败: {str(e)}")
        finally:
            # 恢复原工作目录
            os.chdir(original_cwd)

    @classmethod
    async def _start_detection_process(
        cls,
        task_info: SurveillanceTask,
        stream_info: SurveillanceStream,
        algorithm_config: Dict[str, Any],
        query_db: AsyncSession
    ) -> asyncio.Task:
        """
        启动检测进程
        """
        # 先验证模型是否能正确初始化
        await cls._validate_model_initialization(task_info, algorithm_config)

        async def detection_loop():
            """检测循环 - 优化版本，参考yolo_ROI_ai.py的高效设计"""
            try:
                # 提前提取任务信息，避免数据库会话问题
                task_id = task_info.task_id
                algorithm_id = task_info.algorithm_id
                algorithm_type = task_info.algorithm_type
                stream_id = stream_info.stream_id
                rtsp_url = stream_info.rtsp_url

                # ==================== 配置缓存优化 ====================
                # 一次性解析并缓存所有配置，避免每帧重复解析
                cached_config = cls._cache_task_config(task_info)
                algorithm_config = cached_config['algorithm_config']
                bbox_config_dict = cached_config['bbox_config']
                alert_config_dict = cached_config['alert_config']

                # 提取并缓存区域配置（用于快速区域判断）
                detection_areas = bbox_config_dict.get('detection_areas', [])
                polygons_config = bbox_config_dict.get('polygons', [])
                detection_lines = bbox_config_dict.get('detection_lines', [])
                lines_config = bbox_config_dict.get('lines', [])

                logger.info(f"任务{task_id}配置缓存完成: 区域{len(detection_areas + polygons_config)}个, 线段{len(detection_lines + lines_config)}个")

                # 模仿测试代码的方式：直接在算法包目录下导入和初始化
                algorithm_base_path = "D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968"
                algorithm_dir = f"{algorithm_base_path}/{algorithm_id}"

                # 保存当前工作目录
                original_cwd = os.getcwd()

                try:
                    # 在切换目录之前，先确保YOLOv5路径在sys.path中
                    import sys
                    yolov5_absolute_path = "D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master"
                    if os.path.exists(yolov5_absolute_path) and yolov5_absolute_path not in sys.path:
                        sys.path.insert(0, yolov5_absolute_path)
                        logger.info(f"添加YOLOv5路径: {yolov5_absolute_path}")

                    # 预先导入YOLOv5的utils模块，解决TryExcept导入问题
                    try:
                        utils_path = os.path.join(yolov5_absolute_path, "utils")
                        if utils_path not in sys.path:
                            sys.path.insert(0, utils_path)

                        # 手动导入utils模块并注册到sys.modules
                        import importlib.util
                        utils_init_path = os.path.join(utils_path, "__init__.py")
                        spec = importlib.util.spec_from_file_location("utils", utils_init_path)
                        utils_module = importlib.util.module_from_spec(spec)
                        sys.modules["utils"] = utils_module
                        spec.loader.exec_module(utils_module)
                        logger.info("成功预导入YOLOv5 utils模块")
                    except Exception as e:
                        logger.warning(f"预导入YOLOv5 utils模块失败: {e}")

                    # 切换到算法包目录
                    os.chdir(algorithm_dir)
                    logger.info(f"切换到算法包目录: {algorithm_dir}")

                    # 添加算法包路径到sys.path
                    if algorithm_dir not in sys.path:
                        sys.path.insert(0, algorithm_dir)

                    # 添加model目录到sys.path
                    model_dir = os.path.join(algorithm_dir, "model")
                    if model_dir not in sys.path:
                        sys.path.insert(0, model_dir)

                    # 强制清除模块缓存，确保使用最新代码
                    module_names_to_reload = ['zql_detect', 'model']
                    for module_name in module_names_to_reload:
                        if module_name in sys.modules:
                            importlib.reload(sys.modules[module_name])
                            logger.info(f"重新加载模块: {module_name}")

                    # 直接导入模型（模仿测试代码）
                    from zql_detect import Model
                    logger.info("成功导入智驱力模型")

                    # ==================== 模型参数配置（从数据库获取） ====================
                    # 默认模型参数（如果数据库中没有配置则使用这些默认值）
                    model_config = {
                        'img_size': 640,        # 输入图像尺寸
                        'conf_thres': 0.25,     # 置信度阈值
                        'nms_thres': 0.45       # NMS阈值
                    }

                    # 从数据库配置中提取模型参数
                    model_config = cls._extract_model_parameters_from_config(algorithm_config, model_config)
                    logger.info(f"最终模型配置: {model_config}")

                    # 创建模型实例（完全模仿测试代码）
                    model = Model(
                        acc_id=0,
                        name=task_info.algorithm_id,
                        conf=model_config
                    )

                    if not model.status:
                        logger.error(f"智驱力模型初始化失败: {task_info.algorithm_id}")
                        return

                    logger.info(f"智驱力模型初始化成功")
                    logger.info(f"   - 设备: {getattr(model, 'device', 'unknown')}")
                    logger.info(f"   - 图像尺寸: {getattr(model, 'img_size', 'unknown')}")
                    logger.info(f"   - 置信度阈值: {getattr(model, 'conf_thres', 'unknown')}")
                    logger.info(f"   - NMS阈值: {getattr(model, 'nms_thres', 'unknown')}")

                    # 导入后处理器
                    postprocessor_dir = os.path.join(algorithm_dir, "postprocessor")
                    if postprocessor_dir not in sys.path:
                        sys.path.insert(0, postprocessor_dir)

                    # 动态导入后处理器模块
                    postprocessor_module_name = task_info.algorithm_id
                    postprocessor_module = __import__(postprocessor_module_name)

                    # 创建后处理器实例
                    postprocessor = postprocessor_module.Postprocessor(0, algorithm_id)
                    logger.info("智驱力后处理器初始化成功")

                except Exception as e:
                    logger.error(f"模型或后处理器初始化失败: {e}")
                    import traceback
                    traceback.print_exc()
                    return
                finally:
                    # 恢复原工作目录
                    os.chdir(original_cwd)
                
                # 打开视频流，设置超时参数
                cap = cv2.VideoCapture(rtsp_url, cv2.CAP_FFMPEG)

                # 设置连接和读取超时（30秒）
                cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, 30000)  # 连接超时30秒
                cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 30000)  # 读取超时30秒

                if not cap.isOpened():
                    error_msg = f"视频流连接超时或失败: {rtsp_url}"
                    logger.error(error_msg)
                    # 更新任务状态为失败，并记录错误信息
                    await cls._update_task_status_with_error(query_db, task_id, '0', error_msg)
                    return
                
                frame_count = 0
                consecutive_failures = 0  # 连续失败计数
                max_consecutive_failures = 10  # 最大连续失败次数
                last_successful_read = time.time()  # 上次成功读取时间
                stream_timeout = 60  # 视频流超时时间（秒）

                while True:
                    ret, frame = cap.read()
                    if not ret:
                        consecutive_failures += 1
                        current_time = time.time()

                        # 检查是否超时
                        if current_time - last_successful_read > stream_timeout:
                            error_msg = f"视频流读取超时 {stream_timeout}秒: {stream_info.rtsp_url}"
                            logger.error(error_msg)
                            await cls._update_task_status_with_error(query_db, task_id, '0', error_msg)
                            break

                        # 检查连续失败次数
                        if consecutive_failures >= max_consecutive_failures:
                            error_msg = f"视频流连续读取失败 {max_consecutive_failures} 次: {stream_info.rtsp_url}"
                            logger.error(error_msg)
                            await cls._update_task_status_with_error(query_db, task_id, '0', error_msg)
                            break

                        logger.warning(f"读取视频帧失败 ({consecutive_failures}/{max_consecutive_failures}): {stream_info.rtsp_url}")
                        await asyncio.sleep(1)
                        continue

                    # 成功读取帧，重置计数器
                    consecutive_failures = 0
                    last_successful_read = time.time()
                    
                    frame_count += 1
                    
                    # 每隔几帧进行一次检测（降低CPU使用率）
                    # 修改：跳帧时也推送监控帧，保持画面流畅更新
                    if frame_count % 2 != 0:  # 改为每2帧检测一次，提高流畅度
                        # 跳过检测的帧也要推送到前端，保持实时监控画面更新
                        cls._push_monitor_frame(task_id, frame, [], None)
                        continue
                    
                    # 进行目标检测（使用智驱力模型的标准接口）
                    try:
                        # 调用智驱力模型的infer方法
                        detection_result = model.infer(frame)

                        # 详细打印检测结果用于调试
                        logger.info(f"🔍 任务{task_id} - 检测结果类型: {type(detection_result)}")
                        if isinstance(detection_result, list):
                            logger.info(f"🔍 任务{task_id} - 检测结果数量: {len(detection_result)}")
                            if len(detection_result) > 0:
                                logger.info(f"🔍 任务{task_id} - 第一个检测结果: {detection_result[0]}")
                                logger.info(f"🔍 任务{task_id} - 前3个检测结果: {detection_result[:3]}")
                        elif isinstance(detection_result, dict):
                            logger.info(f"🔍 任务{task_id} - 检测结果字典键: {list(detection_result.keys())}")
                            if 'boxes' in detection_result:
                                logger.info(f"🔍 任务{task_id} - 检测框数量: {len(detection_result['boxes'])}")
                        else:
                            logger.info(f"🔍 任务{task_id} - 检测结果内容: {detection_result}")

                        # 配置后处理器参数（从数据库配置中提取）
                        cls._configure_postprocessor_from_db(postprocessor, algorithm_config)

                        # 根据算法类型调用不同的后处理器接口
                        # algorithm_type 已经在函数开始时提取

                        if algorithm_type == 'person_intrusion':
                            # 人员入侵算法：使用2参数接口
                            model_data = {
                                'model': {
                                    'engine_result': detection_result,
                                    'model_conf': {
                                        'conf_thres': getattr(model, 'conf_thres', 0.25),
                                        'labels': ['person']
                                    }
                                }
                            }

                            result_dict = {
                                'hit': False,
                                'message': '',
                                'data': {
                                    'bbox': {
                                        'rectangles': [],
                                        'polygons': {}
                                    }
                                }
                            }

                            success = postprocessor.process(model_data, result_dict)
                            alert_result = result_dict if success else None

                            # 添加缓存的配置信息（用于绘制）
                            if alert_result and isinstance(alert_result, dict):
                                # 确保有details字段
                                if 'details' not in alert_result:
                                    alert_result['details'] = {}

                                # 使用缓存的配置信息
                                alert_result['details']['configured_areas'] = detection_areas + polygons_config
                                alert_result['details']['configured_lines'] = detection_lines + lines_config

                                logger.debug(f"智驱力算法 - 使用缓存配置: 区域{len(detection_areas + polygons_config)}个, 线段{len(detection_lines + lines_config)}个")

                        elif algorithm_type == 'car_counting':
                            # 车辆计数算法：使用3参数接口
                            result_data = {
                                'detections': detection_result,
                                'frame_id': frame_count,
                                'timestamp': time.time()
                            }

                            # 使用缓存的配置信息
                            alert_params = alert_config_dict.get('alert_params', {})

                            # 添加调试日志
                            logger.debug(f"任务配置 - 检测区域: {len(detection_areas)}个, 多边形: {len(polygons_config)}个")
                            logger.debug(f"任务配置 - 检测线段: {len(detection_lines)}个, 计数线: {len(lines_config)}个")
                            if detection_areas:
                                logger.debug(f"第一个检测区域: {detection_areas[0]}")
                            if detection_lines:
                                logger.debug(f"第一个检测线段: {detection_lines[0]}")

                            # 获取置信度和NMS阈值，使用统一的优先级获取方法
                            # 默认值
                            confidence_threshold = 0.25
                            nms_threshold = 0.5
                            input_size = 640

                            # 使用统一的优先级获取方法，支持从model_parameters字段中获取
                            try:
                                confidence_threshold = cls._get_config_value_with_priority(
                                    algorithm_config,
                                    ['confidence_threshold', 'conf_thres'],
                                    default_value=confidence_threshold,
                                    required=False
                                )
                                logger.info(f"从统一配置获取置信度阈值: {confidence_threshold}")
                            except Exception as e:
                                logger.warning(f"获取置信度阈值失败，使用默认值: {e}")

                            try:
                                nms_threshold = cls._get_config_value_with_priority(
                                    algorithm_config,
                                    ['nms_threshold', 'nms_thres'],
                                    default_value=nms_threshold,
                                    required=False
                                )
                                logger.info(f"从统一配置获取NMS阈值: {nms_threshold}")
                            except Exception as e:
                                logger.warning(f"获取NMS阈值失败，使用默认值: {e}")

                            try:
                                input_size = cls._get_config_value_with_priority(
                                    algorithm_config,
                                    ['input_size', 'img_size'],
                                    default_value=input_size,
                                    required=False
                                )
                                logger.info(f"从统一配置获取输入尺寸: {input_size}")
                            except Exception as e:
                                logger.warning(f"获取输入尺寸失败，使用默认值: {e}")

                            config_data = {
                                # 模型参数
                                'model_parameters': {
                                    'confidence_threshold': confidence_threshold,
                                    'nms_threshold': nms_threshold,
                                    'input_size': input_size
                                },
                                
                                # 告警配置
                                'alert_parameters': {
                                    'enable_alert': alert_params.get('enable_alert', True),
                                    'alert_interval': alert_params.get('alert_interval', 5),
                                    'alert_threshold': alert_params.get('alert_threshold', 1),
                                    'alert_message': alert_params.get('alert_message', '车辆计数告警')
                                },

                                # 计数线配置（支持两种格式）
                                'detection_lines': detection_lines,
                                'lines': lines_config,

                                # 检测区域配置（支持两种格式）
                                'detection_areas': detection_areas,
                                'polygons': polygons_config,

                                # 图像尺寸（用于坐标转换）
                                'image_width': frame.shape[1],
                                'image_height': frame.shape[0]
                            }

                            logger.debug(f"车辆计数配置: 计数线{len(detection_lines + lines_config)}条, 检测区域{len(detection_areas + polygons_config)}个")

                            # 不在这里分配跟踪ID，让后处理器负责ID生成
                            # 后处理器会根据时间和计数来生成合理的车辆ID
                            logger.debug(f"传递给后处理器的检测结果数量: {len(result_data) if isinstance(result_data, list) else 'N/A'}")

                            alert_result = postprocessor.process(result_data, frame, config_data)

                            # 详细打印后处理结果用于调试
                            logger.info(f"⚠️ 任务{task_id} - 后处理结果类型: {type(alert_result)}")
                            if alert_result:
                                logger.info(f"⚠️ 任务{task_id} - 后处理结果键: {list(alert_result.keys()) if isinstance(alert_result, dict) else 'N/A'}")
                                if isinstance(alert_result, dict):
                                    logger.info(f"⚠️ 任务{task_id} - hit: {alert_result.get('hit')}")
                                    logger.info(f"⚠️ 任务{task_id} - message: {alert_result.get('message')}")
                                    details = alert_result.get('details', {})
                                    if details:
                                        logger.info(f"⚠️ 任务{task_id} - details键: {list(details.keys())}")

                                        # 检查车辆跟踪信息
                                        if 'vehicle_tracking' in details:
                                            vehicle_tracking = details['vehicle_tracking']
                                            logger.info(f"🚗 任务{task_id} - 车辆跟踪信息: {vehicle_tracking}")
                                            logger.info(f"🚗 任务{task_id} - 车辆ID列表: {vehicle_tracking.get('vehicle_ids', [])}")
                                            logger.info(f"🚗 任务{task_id} - 唯一车辆ID: {vehicle_tracking.get('unique_vehicle_ids', [])}")

                                        # 检查detections中的track_id
                                        if 'detections' in details and details['detections']:
                                            detections = details['detections']
                                            logger.info(f"⚠️ 任务{task_id} - 检测结果数量: {len(detections)}")
                                            for i, detection in enumerate(detections[:3]):  # 只显示前3个
                                                track_id = detection.get('track_id')
                                                bbox = detection.get('xyxy', [])
                                                logger.info(f"⚠️ 任务{task_id} - 检测{i+1}: track_id={track_id}, bbox={bbox}")
                                                if track_id is None:
                                                    logger.warning(f"⚠️ 任务{task_id} - 检测{i+1}缺少track_id字段")
                            else:
                                logger.info(f"⚠️ 任务{task_id} - 无后处理结果")

                            # 确保告警结果包含配置的区域和线段信息（用于绘制）
                            if alert_result and isinstance(alert_result, dict):
                                if 'details' not in alert_result:
                                    alert_result['details'] = {}

                                # 添加配置的区域信息
                                alert_result['details']['configured_areas'] = detection_areas + polygons_config
                                alert_result['details']['configured_lines'] = detection_lines + lines_config

                        else:
                            # 默认处理方式
                            logger.warning(f"未知算法类型: {algorithm_type}，使用默认处理方式")
                            alert_result = {
                                'hit': len(detection_result) > 0,
                                'message': f'检测到 {len(detection_result)} 个目标',
                                'data': {'detections': detection_result},
                                'details': {
                                    'configured_areas': detection_areas + polygons_config,
                                    'configured_lines': detection_lines + lines_config,
                                    'total_detections': len(detection_result)
                                }
                            }
                        logger.debug(f"后处理结果: {alert_result}")

                    except Exception as e:
                        logger.error(f"检测或后处理失败: {e}")
                        detection_result = []
                        alert_result = None

                    # 确保alert_result包含配置区域信息（用于绘制监控范围）
                    if alert_result is None:
                        # 使用缓存的配置信息创建默认alert_result
                        alert_result = {
                            'hit': False,
                            'message': '监控中',
                            'details': {
                                'detections': detection_result or [],
                                'configured_areas': detection_areas + polygons_config,
                                'configured_lines': detection_lines + lines_config
                            }
                        }
                        logger.debug(f"创建默认alert_result，包含配置区域{len(detection_areas + polygons_config)}个")

                    # 推送实时监控流 - 只有告警时才推送，检测结果不推送
                    try:
                        # 检查是否有告警
                        has_alert = bool(alert_result and alert_result.get('hit'))

                        if has_alert:
                            # 有告警时推送到前端
                            # 使用数据库配置的alert_message作为推送内容
                            alert_message = alert_params.get('alert_message', f'{algorithm_id}告警')
                            alert_level = alert_params.get('alert_level', '2')  # 默认中级告警
                            alert_type = algorithm_id  # 使用算法ID作为告警类型

                            # 更新alert_result中的message和其他信息
                            if alert_result and isinstance(alert_result, dict):
                                alert_result['message'] = alert_message
                                alert_result['level'] = alert_level
                                alert_result['type'] = alert_type

                            cls._push_monitor_frame(task_id, frame, detection_result or [], alert_result)
                            logger.info(f"📢 告警推送: 任务{task_id}, 消息: {alert_message}, 级别: {alert_level}")
                        else:
                            # 无告警时也要推送帧，保持实时监控画面更新
                            # 重要修改：使用算法包的处理结果而不是原始检测结果
                            # 这样可以确保颜色、区域过滤等信息正确传递
                            if frame_count % 3 == 0:  # 每3帧推送一次，提高更新频率
                                # 使用算法包的处理结果，即使没有告警也包含正确的颜色和区域信息
                                cls._push_monitor_frame(task_id, frame, detection_result or [], alert_result)

                                # 统计算法包处理后的检测数量
                                processed_count = 0
                                if alert_result and isinstance(alert_result, dict):
                                    details = alert_result.get('details', {})
                                    detections = details.get('detections', [])
                                    processed_count = len(detections)

                                logger.debug(f"🔍 正常推送: 任务{task_id}, 帧数{frame_count}, 算法包处理后{processed_count}个目标，无告警")

                    except Exception as push_error:
                        logger.error(f"推送监控帧失败: {push_error}")
                        # 即使推送失败也要继续，确保检测循环不中断

                    # 如果有告警，保存告警记录
                    if alert_result and alert_result.get('hit'):
                        try:
                            # 使用线程池执行器来运行同步的数据库操作
                            # 这样可以避免 greenlet 上下文问题
                            # 只传递必要的数据，避免数据库对象脱离会话的问题
                            await cls._save_alert_record_async(
                                task_id, stream_id, frame, alert_result,
                                algorithm_id, alert_config_dict
                            )
                            logger.info(f"成功保存告警记录: 任务{task_id}")
                        except Exception as save_error:
                            # 特殊处理数据库连接错误
                            error_str = str(save_error)
                            if "Command Out of Sync" in error_str:
                                logger.error(f"数据库连接状态异常，跳过本次告警记录保存: task_id={task_id}")
                                logger.error(f"建议检查数据库连接池配置或重启服务")
                            elif "Lost connection" in error_str or "MySQL server has gone away" in error_str:
                                logger.error(f"数据库连接丢失，跳过本次告警记录保存: task_id={task_id}")
                                logger.error(f"系统将在下次告警时尝试重新连接数据库")
                            elif "greenlet_spawn" in error_str:
                                logger.error(f"异步上下文错误，跳过本次告警记录保存: task_id={task_id}")
                                logger.error(f"错误详情: {save_error}")
                            else:
                                logger.error(f"保存告警记录过程失败: task_id={task_id}, 错误: {save_error}")
                            # 不重新抛出异常，确保检测循环继续

                    # 更新任务运行次数
                    if frame_count % 100 == 0:  # 每100帧更新一次
                        try:
                            await cls._update_task_run_count(query_db, task_id)
                            logger.debug(f"成功更新任务运行次数 - frame_count: {frame_count}")
                        except Exception as update_error:
                            logger.error(f"更新任务运行次数失败: {update_error}")

                    await asyncio.sleep(0.1)  # 控制检测频率
                    
            except asyncio.CancelledError:
                logger.info(f"检测任务被取消: {task_id}")
            except Exception as e:
                error_str = str(e)
                logger.error(f"检测过程中发生错误: {e}")

                # 检查是否是视频流超时错误
                if "timeout" in error_str.lower() or "ffmpeg" in error_str.lower():
                    error_msg = f"视频流连接超时，请检查网络连接和视频流地址: {stream_info.rtsp_url}"
                    logger.error(error_msg)
                    await cls._update_task_status_with_error(query_db, task_id, '0', error_msg)
                else:
                    error_msg = f"检测过程异常: {error_str}"
                    await cls._update_task_status_with_error(query_db, task_id, '0', error_msg)

                import traceback
                traceback.print_exc()
            finally:
                if 'cap' in locals():
                    cap.release()
        
        # 创建并启动异步任务
        task = asyncio.create_task(detection_loop())
        return task

    @classmethod
    def _configure_postprocessor_from_db(cls, postprocessor, algorithm_config: Dict[str, Any]):
        """从数据库配置中设置后处理器参数"""
        try:
            # 设置检测区域
            if 'detection_areas' in algorithm_config:
                detection_areas = algorithm_config['detection_areas']
                if isinstance(detection_areas, list) and len(detection_areas) > 0:
                    # 转换为智驱力格式的多边形
                    polygons = {}
                    for i, area in enumerate(detection_areas):
                        if 'points' in area and len(area['points']) >= 3:
                            polygon_points = []
                            for point in area['points']:
                                if isinstance(point, dict) and 'x' in point and 'y' in point:
                                    polygon_points.append([point['x'], point['y']])

                            if len(polygon_points) >= 3:
                                polygons[f'polygon_{i}'] = {
                                    'polygon': polygon_points,
                                    'color': [0, 255, 0],  # 绿色
                                    'thickness': 2
                                }

                    # 设置到后处理器的reserved_args中
                    if not hasattr(postprocessor, 'reserved_args'):
                        postprocessor.reserved_args = {}
                    postprocessor.reserved_args['polygons'] = polygons
                    logger.info(f"设置检测区域: {len(polygons)}个多边形")

            # 设置置信度阈值，确保user_config优先级最高
            # 使用统一的优先级获取方法 - 必需参数
            conf_threshold = cls._get_config_value_with_priority(
                algorithm_config,
                ['confidence_threshold', 'conf_thres'],  # 按优先级排序
                required=True
            )

            # 从后处理器config_data获取（最高优先级，通常来自user_config）
            if hasattr(postprocessor, 'config_data') and postprocessor.config_data:
                model_params = postprocessor.config_data.get('model_parameters', {})
                if 'confidence_threshold' in model_params:
                    conf_threshold = model_params['confidence_threshold']
                    logger.info(f"从config_data获取置信度阈值（最高优先级）: {conf_threshold}")

            if not hasattr(postprocessor, 'reserved_args'):
                postprocessor.reserved_args = {}
            postprocessor.reserved_args['confidence_threshold'] = conf_threshold
            logger.info(f"最终设置后处理器置信度阈值: {conf_threshold}")

            # 设置告警间隔
            if 'alert_interval' in algorithm_config:
                alert_interval = algorithm_config['alert_interval']
                if not hasattr(postprocessor, 'reserved_args'):
                    postprocessor.reserved_args = {}
                postprocessor.reserved_args['alert_interval'] = alert_interval
                logger.info(f"设置告警间隔: {alert_interval}秒")

            # 设置策略（如果有）
            if 'strategy' in algorithm_config:
                strategy = algorithm_config['strategy']
                if not hasattr(postprocessor, 'reserved_args'):
                    postprocessor.reserved_args = {}
                postprocessor.reserved_args['strategy'] = strategy
                logger.info(f"设置检测策略: {strategy}")

        except Exception as e:
            logger.error(f"配置后处理器失败: {e}")

    @classmethod
    def _load_algorithm_modules(cls, algorithm_id: str, platform: str = "KS968"):
        """
        动态加载智驱力算法模块
        """
        try:
            # 使用智驱力的真实目录结构
            algorithm_path = cls.ALGORITHM_BASE_PATH / platform / algorithm_id

            # 智驱力的模型路径（model目录）
            model_path = algorithm_path / "model"

            # 查找zql_detect.py文件（智驱力的主模型文件）
            model_file = model_path / "zql_detect.py"
            if not model_file.exists():
                logger.error(f"智驱力模型文件不存在: {model_file}")
                return None, None

            # 智驱力的后处理路径
            postprocessor_path = algorithm_path / "postprocessor"

            # 查找算法名.py文件（智驱力的后处理文件）
            postprocessor_file = postprocessor_path / f"{algorithm_id}.py"
            if not postprocessor_file.exists():
                logger.error(f"智驱力后处理文件不存在: {postprocessor_file}")
                return None, None

            logger.info(f"加载智驱力算法模块: {algorithm_id}")
            logger.info(f"模型文件: {model_file}")
            logger.info(f"后处理文件: {postprocessor_file}")

            # 动态导入智驱力模块
            import importlib.util

            # 加载模型模块
            model_spec = importlib.util.spec_from_file_location(f"{algorithm_id}_model", model_file)
            model_module = importlib.util.module_from_spec(model_spec)
            sys.modules[f"{algorithm_id}_model"] = model_module
            model_spec.loader.exec_module(model_module)

            # 加载后处理模块
            postprocessor_spec = importlib.util.spec_from_file_location(f"{algorithm_id}_postprocessor", postprocessor_file)
            postprocessor_module = importlib.util.module_from_spec(postprocessor_spec)
            sys.modules[f"{algorithm_id}_postprocessor"] = postprocessor_module
            postprocessor_spec.loader.exec_module(postprocessor_module)

            return model_module, postprocessor_module

        except Exception as e:
            logger.error(f"加载智驱力算法模块失败: {algorithm_id}, 错误: {e}")
            import traceback
            traceback.print_exc()
            return None, None

    @classmethod
    async def _run_detection(cls, model_module, frame, algorithm_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        运行智驱力目标检测
        使用数据库中的配置参数，不再依赖智驱力算法包的默认配置
        """
        try:
            # 检查是否有模型类
            if not hasattr(model_module, 'Model'):
                logger.error("智驱力模块中没有找到Model类")
                return None

            # 从数据库配置中提取模型参数
            # 默认模型参数
            model_config = {
                'img_size': 640,        # 输入图像尺寸
                'conf_thres': 0.25,     # 置信度阈值
                'nms_thres': 0.45       # NMS阈值
            }

            # 使用统一的优先级获取方法来提取模型参数 - 必需参数
            conf_thres = cls._get_config_value_with_priority(
                algorithm_config,
                ['confidence_threshold', 'conf_thres'],
                required=True
            )
            model_config['conf_thres'] = conf_thres

            nms_thres = cls._get_config_value_with_priority(
                algorithm_config,
                ['nms_threshold', 'nms_thres'],
                required=True
            )
            model_config['nms_thres'] = nms_thres

            img_size = cls._get_config_value_with_priority(
                algorithm_config,
                ['input_size', 'img_size'],
                model_config.get('img_size', 640)
            )
            model_config['img_size'] = img_size

            logger.info(f"智驱力模型配置: {model_config}")

            # 创建智驱力模型实例
            model_instance = model_module.Model(
                acc_id=0,  # 加速器ID
                name="detection_model",  # 统一的模型名称
                conf=model_config
            )

            if not model_instance.status:
                logger.error("智驱力模型初始化失败")
                return None

            # 调用智驱力模型进行推理
            result = model_instance.infer(frame)
            if result is not None:
                logger.debug(f"智驱力模型检测到 {len(result[0]) if result[0] is not None else 0} 个目标")
                return {"detection_model": result}
            else:
                logger.warning("智驱力模型推理返回空结果")
                return None

        except Exception as e:
            logger.error(f"智驱力目标检测失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    @classmethod
    def _parse_zhiquli_result(cls, raw_result: list, frame_shape: tuple) -> Dict[str, Any]:
        """
        解析智驱力模型的原始输出

        Args:
            raw_result: 智驱力模型的原始输出
            frame_shape: 原始帧的形状 (height, width, channels)

        Returns:
            标准化的检测结果
        """
        try:
            height, width = frame_shape[:2]

            boxes = []
            scores = []
            labels = []

            for detection in raw_result:
                if isinstance(detection, dict):
                    # 提取边界框坐标（相对坐标转绝对坐标）
                    if 'bbox' in detection:
                        bbox = detection['bbox']
                        if len(bbox) >= 4:
                            x1 = int(bbox[0] * width)
                            y1 = int(bbox[1] * height)
                            x2 = int(bbox[2] * width)
                            y2 = int(bbox[3] * height)
                            boxes.append([x1, y1, x2, y2])

                    # 提取置信度
                    if 'confidence' in detection:
                        scores.append(detection['confidence'])
                    elif 'score' in detection:
                        scores.append(detection['score'])
                    else:
                        scores.append(0.5)  # 默认置信度

                    # 提取标签
                    if 'class_id' in detection:
                        labels.append(detection['class_id'])
                    elif 'label' in detection:
                        labels.append(detection['label'])
                    else:
                        labels.append(0)  # 默认标签

            return {
                'boxes': boxes,
                'scores': scores,
                'labels': labels,
                'raw_result': raw_result  # 保留原始结果用于后处理
            }

        except Exception as e:
            logger.error(f"解析检测结果失败: {e}")
            return {'boxes': [], 'scores': [], 'labels': [], 'raw_result': raw_result}

    @classmethod
    async def _run_postprocessing(
        cls,
        postprocessor_module,
        detection_result: Dict[str, Any],
        frame,
        algorithm_config: Dict[str, Any],
        task_info: SurveillanceTask
    ) -> Optional[Dict[str, Any]]:
        """
        运行后处理逻辑
        """
        try:
            # 创建后处理器实例
            if hasattr(postprocessor_module, 'Postprocessor'):
                postprocessor_instance = postprocessor_module.Postprocessor(
                    source_id=task_info.stream_id,
                    alg_name=task_info.algorithm_id
                )

                # 设置后处理器的配置
                cls._configure_postprocessor_from_db(postprocessor_instance, algorithm_config)

                logger.debug(f"创建后处理器实例: {task_info.algorithm_id}")
            else:
                logger.error("后处理器模块中没有Postprocessor类")
                return cls._custom_postprocessing(detection_result, algorithm_config, frame.shape)

            # 调用后处理器
            try:
                # 后处理器的标准接口
                # 输入格式：detection_result (模型输出), filter_result (过滤后的结果)
                filter_result = cls._prepare_filter_result(detection_result, algorithm_config)

                # 添加图像尺寸信息到配置中
                if algorithm_config:
                    algorithm_config['image_width'] = frame.shape[1]  # 图像宽度
                    algorithm_config['image_height'] = frame.shape[0]  # 图像高度
                    logger.debug(f"传递图像尺寸: {frame.shape[1]}x{frame.shape[0]}")

                # 调用后处理器的process方法，传递数据库配置
                postprocess_result = postprocessor_instance._process(detection_result, filter_result, algorithm_config)

                # 解析后处理结果
                alert_result = cls._parse_postprocess_result(
                    postprocess_result,
                    detection_result,
                    algorithm_config,
                    frame.shape
                )

                return alert_result

            except Exception as e:
                logger.warning(f"后处理器调用失败: {e}，使用自定义后处理")
                import traceback
                traceback.print_exc()
                # 如果后处理器失败，使用自定义后处理逻辑
                return cls._custom_postprocessing(detection_result, algorithm_config, frame.shape)

        except Exception as e:
            logger.error(f"后处理失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    @classmethod
    def _prepare_filter_result(cls, detection_result: Dict[str, Any], algorithm_config: Dict[str, Any]) -> Dict:
        """
        准备后处理器需要的filter_result格式
        """
        try:
            # 后处理器期望的格式：{model_name: rectangles}
            # rectangles格式：[[x1, y1, x2, y2, confidence, class_id], ...]
            filter_result = {}

            for model_name, model_result in detection_result.items():
                if model_result is not None and len(model_result) >= 3:
                    boxes, classes, scores = model_result
                    if boxes is not None and len(boxes) > 0:
                        rectangles = []
                        for i in range(len(boxes)):
                            x1, y1, x2, y2 = boxes[i]
                            confidence = scores[i] if i < len(scores) else 0.5
                            class_id = int(classes[i]) if i < len(classes) else 0
                            rectangles.append([x1, y1, x2, y2, confidence, class_id])
                        filter_result[model_name] = rectangles
                        logger.debug(f"为模型 {model_name} 准备了 {len(rectangles)} 个检测框")

            return filter_result

        except Exception as e:
            logger.error(f"准备后处理器filter_result格式失败: {e}")
            return {}

    @classmethod
    def _prepare_zhiquli_input(cls, detection_result: Dict[str, Any], task_config: Dict[str, Any]) -> Dict:
        """
        准备智驱力后处理器需要的输入格式
        """
        try:
            # 获取置信度阈值，优先使用user_config中的值
            confidence_threshold = 0.5  # 默认值

            # 优先从model_parameters获取（这里包含了user_config的值）
            model_params = task_config.get('model_parameters', {})
            if model_params:
                confidence_threshold = model_params.get('confidence_threshold', confidence_threshold)
                logger.debug(f"从model_parameters获取置信度阈值: {confidence_threshold}")
            else:
                # 兼容旧的配置格式
                algorithm_conf = task_config.get('algorithm_config', {})
                old_model_params = algorithm_conf.get('model_params', {})
                confidence_threshold = old_model_params.get('confidence_threshold', confidence_threshold)
                logger.debug(f"从旧格式获取置信度阈值: {confidence_threshold}")

            # 智驱力后处理器期望的输入格式
            zhiquli_input = {
                'model_name': {  # 模型名称作为key
                    'model_conf': {
                        'conf_thres': confidence_threshold,
                        'label': ['car', 'truck', 'bus', 'person'],  # 根据算法类型调整
                        'color': {
                            'car': [255, 0, 0],
                            'truck': [0, 255, 0],
                            'bus': [0, 0, 255],
                            'person': [255, 255, 0]
                        }
                    },
                    'engine_result': []
                }
            }

            # 转换检测结果为智驱力格式
            boxes = detection_result.get('boxes', [])
            scores = detection_result.get('scores', [])
            labels = detection_result.get('labels', [])

            for i, (box, score, label) in enumerate(zip(boxes, scores, labels)):
                if len(box) >= 4 and score >= confidence_threshold:
                    zhiquli_input['model_name']['engine_result'].append({
                        'xyxy': box,  # [x1, y1, x2, y2]
                        'conf': score,
                        'label': label
                    })

            return zhiquli_input

        except Exception as e:
            logger.error(f"准备智驱力输入格式失败: {e}")
            return {}

    @classmethod
    def _parse_zhiquli_postprocess_result(
        cls,
        postprocess_result: Any,
        detection_result: Dict[str, Any],
        algorithm_config: Dict[str, Any],
        frame_shape: tuple
    ) -> Dict[str, Any]:
        """
        解析后处理器的结果
        """
        try:
            # 自定义格式的后处理结果
            hit = False
            message = ""
            details = {}

            if isinstance(postprocess_result, dict):
                # 检查是否有告警
                hit = postprocess_result.get('hit', False)
                message = postprocess_result.get('message', '')
                details = postprocess_result.get('details', {})

                # 统计告警目标 - 简化的提取逻辑
                alert_targets = []
                detections = details.get('detections', [])

                # 如果算法包判断为告警(hit=True)，则提取所有检测结果作为告警目标
                if hit and detections:
                    for det in detections:
                        # 获取算法包提供的颜色，如果没有则使用None
                        original_color = det.get('color')

                        alert_targets.append({
                            'box': det.get('xyxy', []),
                            'score': det.get('conf', 0.0),
                            'label': det.get('label', 'vehicle'),
                            'alert_type': 'vehicle_counting',
                            'original_color': original_color,  # 保留算法包的原始颜色
                            'track_id': det.get('track_id'),  # 保留车辆跟踪ID
                            'alert_reason': det.get('alert_reason', '')  # 保留告警原因
                        })

                if hit and alert_targets:
                    message = f"检测到 {len(alert_targets)} 个告警目标"
                    details = {
                        'alert_targets': alert_targets,
                        'total_detections': len(detections),
                        'algorithm_result': postprocess_result,
                        # 传递后处理模块的区域和线段信息
                        'polygons': details.get('polygons', {}),
                        'lines': details.get('lines', {}),
                        'detections': detections  # 传递原始检测结果
                    }
                else:
                    message = "未检测到告警目标"
                    details = {
                        'total_detections': len(detections),
                        'algorithm_result': postprocess_result,
                        # 传递后处理模块的区域和线段信息
                        'polygons': details.get('polygons', {}),
                        'lines': details.get('lines', {}),
                        'detections': detections  # 传递原始检测结果
                    }

            # 添加通用信息
            details.update({
                'frame_shape': frame_shape,
                'timestamp': datetime.now().isoformat(),
                'algorithm_config': algorithm_config.get('algorithm_config', {})
            })

            return {
                'hit': hit,
                'message': message,
                'details': details,
                'postprocess_result': postprocess_result
            }

        except Exception as e:
            logger.error(f"解析后处理结果失败: {e}")
            # 降级到自定义后处理
            return cls._custom_postprocessing(detection_result, algorithm_config, frame_shape)

    @classmethod
    def _parse_postprocess_result(
        cls,
        postprocess_result: Any,
        detection_result: Dict[str, Any],
        algorithm_config: Dict[str, Any],
        frame_shape: tuple
    ) -> Dict[str, Any]:
        """
        解析智驱力后处理器的结果
        """
        try:
            # 智驱力后处理器的结果格式可能因算法而异
            # 这里提供一个通用的解析逻辑

            hit = False
            message = ""
            details = {}

            if isinstance(postprocess_result, dict):
                hit = postprocess_result.get('hit', False)
                message = postprocess_result.get('message', '')
                details = postprocess_result.get('details', {})

                # 统计告警目标 - 简化的提取逻辑
                alert_targets = []
                detections = details.get('detections', [])

                # 如果算法包判断为告警(hit=True)，则提取所有检测结果作为告警目标
                if hit and detections:
                    for det in detections:
                        # 获取算法包提供的颜色，如果没有则使用None
                        original_color = det.get('color')

                        alert_targets.append({
                            'box': det.get('xyxy', det.get('bbox', det.get('box', []))),
                            'score': det.get('conf', det.get('confidence', 0.0)),
                            'label': det.get('label', 'vehicle'),
                            'alert_type': 'vehicle_counting',
                            'original_color': original_color,  # 保留算法包的原始颜色
                            'track_id': det.get('track_id'),  # 保留车辆跟踪ID
                            'alert_reason': det.get('alert_reason', '')  # 保留告警原因
                        })

                # 更新消息和详情
                if hit and alert_targets:
                    message = f"检测到 {len(alert_targets)} 个告警目标"
                    details.update({
                        'alert_targets': alert_targets,
                        'total_detections': len(detections)
                    })
                elif hit:
                    message = "检测到告警但无法提取目标位置"
                else:
                    message = "未检测到告警目标"

            elif isinstance(postprocess_result, bool):
                hit = postprocess_result
                message = "检测到目标" if hit else "未检测到目标"
                details = {}
            elif isinstance(postprocess_result, list) and len(postprocess_result) > 0:
                # 如果返回列表，认为有检测结果就是告警
                hit = True
                message = f"检测到 {len(postprocess_result)} 个目标"
                details = {'detections': postprocess_result}

            # 添加检测统计信息
            details.update({
                'detection_count': len(detection_result.get('boxes', [])),
                'frame_shape': frame_shape,
                'timestamp': datetime.now().isoformat()
            })

            return {
                'hit': hit,
                'message': message,
                'details': details,
                'postprocess_result': postprocess_result
            }

        except Exception as e:
            logger.error(f"解析后处理结果失败: {e}")
            return cls._custom_postprocessing(detection_result, {'bbox_config': algorithm_config.get('bbox_config', {}), 'alert_config': algorithm_config.get('alert_config', {})}, frame_shape)

    @classmethod
    def _custom_postprocessing(
        cls,
        detection_result: Dict[str, Any],
        algorithm_config: Dict[str, Any],
        frame_shape: tuple
    ) -> Dict[str, Any]:
        """
        自定义后处理逻辑（当智驱力后处理器不可用时）
        注意：过滤逻辑应该在算法包内部完成，这里只负责结果处理和图片绘制
        """
        try:
            bbox_config = algorithm_config.get('bbox_config', {})
            alert_config = algorithm_config.get('alert_config', {})

            boxes = detection_result.get('boxes', [])
            scores = detection_result.get('scores', [])

            # 获取告警配置
            alert_params = alert_config.get('alert_params', {})
            alert_threshold = alert_params.get('alert_threshold', 1)
            enable_alert = alert_params.get('enable_alert', True)

            logger.debug(f"告警阈值: {alert_threshold}, 启用告警: {enable_alert}")

            # 直接使用检测结果，过滤逻辑已在算法包内部完成
            valid_detections = []
            for i, (box, score) in enumerate(zip(boxes, scores)):
                valid_detections.append({
                    'box': box,
                    'score': score,
                    'label': detection_result.get('labels', [0])[i] if i < len(detection_result.get('labels', [])) else 0
                })

            # 检查是否在配置的区域内
            in_area_detections = cls._check_detections_in_areas(valid_detections, bbox_config, frame_shape)

            # 检查是否跨越配置的线段
            line_crossing_detections = cls._check_detections_cross_lines(valid_detections, bbox_config, frame_shape)

            # 计算总的告警目标数量
            total_alert_detections = len(in_area_detections) + len(line_crossing_detections)

            # 根据配置的告警阈值判断是否触发告警
            hit = False
            if enable_alert and total_alert_detections >= alert_threshold:
                hit = True

            message = ""
            if hit:
                if in_area_detections:
                    message += f"区域内检测到 {len(in_area_detections)} 个目标; "
                if line_crossing_detections:
                    message += f"线段检测到 {len(line_crossing_detections)} 个目标; "
                message = message.rstrip("; ")
                message += f" (阈值: {alert_threshold})"
            else:
                if total_alert_detections > 0:
                    message = f"检测到 {total_alert_detections} 个目标，未达到告警阈值 {alert_threshold}"
                else:
                    message = "未检测到告警目标"

            # 提取配置的区域和线段信息用于绘制
            configured_areas = []
            configured_lines = []

            # 从bbox_config中提取区域配置
            areas = bbox_config.get('areas', [])
            for area in areas:
                configured_areas.append({
                    'name': area.get('name', '区域'),
                    'points': area.get('points', [])
                })

            # 从bbox_config中提取线段配置
            lines = bbox_config.get('lines', [])
            for line in lines:
                configured_lines.append({
                    'name': line.get('name', '线段'),
                    'start_point': line.get('start_point', {}),
                    'end_point': line.get('end_point', {})
                })

            return {
                'hit': hit,
                'message': message,
                'details': {
                    'total_detections': len(valid_detections),
                    'in_area_detections': in_area_detections,
                    'line_crossing_detections': line_crossing_detections,
                    'configured_areas': configured_areas,  # 添加配置的区域
                    'configured_lines': configured_lines,  # 添加配置的线段
                    'frame_shape': frame_shape,
                    'timestamp': datetime.now().isoformat()
                }
            }

        except Exception as e:
            logger.error(f"自定义后处理失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                'hit': False,
                'message': f"后处理失败: {str(e)}",
                'details': {
                    'total_detections': 0,
                    'in_area_detections': [],
                    'line_crossing_detections': [],
                    'configured_areas': [],
                    'configured_lines': [],
                    'frame_shape': frame_shape,
                    'timestamp': datetime.now().isoformat()
                }
            }

    @classmethod
    def _check_detections_in_areas(
        cls,
        detections: List[Dict],
        bbox_config: Dict[str, Any],
        frame_shape: tuple
    ) -> List[Dict]:
        """
        检查检测结果是否在配置的区域内
        """
        try:
            height, width = frame_shape[:2]
            in_area_detections = []

            # 获取检测区域（优先使用前端格式，其次使用智驱力格式）
            detection_areas = bbox_config.get('detection_areas', [])
            polygons = bbox_config.get('polygons', [])

            logger.debug(f"检测区域配置: detection_areas={len(detection_areas)}, polygons={len(polygons)}")

            # 处理前端格式的检测区域
            for area in detection_areas:
                if 'points' in area and len(area['points']) >= 3:
                    try:
                        polygon_points = [(int(p['x'] * width), int(p['y'] * height)) for p in area['points']]

                        for detection in detections:
                            box = detection['box']
                            # 检测框中心点
                            center_x = (box[0] + box[2]) // 2
                            center_y = (box[1] + box[3]) // 2

                            if cls._point_in_polygon((center_x, center_y), polygon_points):
                                in_area_detections.append({
                                    **detection,
                                    'area_id': area.get('id', 'unknown'),
                                    'area_name': area.get('name', 'unknown'),
                                    'area_type': 'frontend'
                                })
                                logger.debug(f"目标在区域 {area.get('name', 'unknown')} 内: {detection}")
                    except Exception as e:
                        logger.error(f"处理前端检测区域失败: {e}")

            # 处理智驱力格式的多边形（如果没有前端格式的区域）
            if not detection_areas:
                for i, polygon in enumerate(polygons):
                    if len(polygon) >= 3:
                        try:
                            polygon_points = [(int(p['x'] * width), int(p['y'] * height)) for p in polygon]

                            for detection in detections:
                                box = detection['box']
                                center_x = (box[0] + box[2]) // 2
                                center_y = (box[1] + box[3]) // 2

                                if cls._point_in_polygon((center_x, center_y), polygon_points):
                                    in_area_detections.append({
                                        **detection,
                                        'area_id': f'polygon_{i}',
                                        'area_name': f'多边形区域{i+1}',
                                        'area_type': 'zhiquli'
                                    })
                                    logger.debug(f"目标在智驱力多边形区域{i+1}内: {detection}")
                        except Exception as e:
                            logger.error(f"处理智驱力多边形区域失败: {e}")

            return in_area_detections

        except Exception as e:
            logger.error(f"检查区域检测失败: {e}")
            return []

    @classmethod
    def _check_detections_cross_lines(
        cls,
        detections: List[Dict],
        bbox_config: Dict[str, Any],
        frame_shape: tuple
    ) -> List[Dict]:
        """
        检查检测结果是否跨越配置的线段
        """
        try:
            height, width = frame_shape[:2]
            line_crossing_detections = []

            # 获取检测线段（优先使用前端格式，其次使用智驱力格式）
            detection_lines = bbox_config.get('detection_lines', [])
            lines = bbox_config.get('lines', [])

            logger.debug(f"检测线段配置: detection_lines={len(detection_lines)}, lines={len(lines)}")

            # 处理前端格式的检测线段
            for line_config in detection_lines:
                if 'points' in line_config and len(line_config['points']) >= 2:
                    try:
                        line_points = [(int(p['x'] * width), int(p['y'] * height)) for p in line_config['points']]

                        for detection in detections:
                            box = detection['box']
                            # 检测框的底边中点（通常用于线段检测）
                            bottom_center = ((box[0] + box[2]) // 2, box[3])

                            if cls._point_near_line(bottom_center, line_points, threshold=20):
                                line_crossing_detections.append({
                                    **detection,
                                    'line_id': line_config.get('id', 'unknown'),
                                    'line_name': line_config.get('name', 'unknown'),
                                    'line_type': 'frontend'
                                })
                                logger.debug(f"目标跨越线段 {line_config.get('name', 'unknown')}: {detection}")
                    except Exception as e:
                        logger.error(f"处理前端检测线段失败: {e}")

            # 如果没有检测线段配置，记录日志
            if not detection_lines:
                logger.debug("未配置检测线段，跳过线段检测")

            return line_crossing_detections

        except Exception as e:
            logger.error(f"检查线段检测失败: {e}")
            return []

    @classmethod
    def _point_in_polygon(cls, point: tuple, polygon: List[tuple]) -> bool:
        """
        判断点是否在多边形内（射线法）
        """
        try:
            x, y = point
            n = len(polygon)
            inside = False

            p1x, p1y = polygon[0]
            for i in range(1, n + 1):
                p2x, p2y = polygon[i % n]
                if y > min(p1y, p2y):
                    if y <= max(p1y, p2y):
                        if x <= max(p1x, p2x):
                            if p1y != p2y:
                                xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                            if p1x == p2x or x <= xinters:
                                inside = not inside
                p1x, p1y = p2x, p2y

            return inside

        except Exception as e:
            logger.error(f"点在多边形判断失败: {e}")
            return False

    @classmethod
    def _point_near_line(cls, point: tuple, line_points: List[tuple], threshold: int = 20) -> bool:
        """
        判断点是否靠近线段
        """
        try:
            if len(line_points) < 2:
                return False

            x, y = point
            min_distance = float('inf')

            # 计算点到线段的最短距离
            for i in range(len(line_points) - 1):
                x1, y1 = line_points[i]
                x2, y2 = line_points[i + 1]

                # 计算点到线段的距离
                A = x - x1
                B = y - y1
                C = x2 - x1
                D = y2 - y1

                dot = A * C + B * D
                len_sq = C * C + D * D

                if len_sq == 0:
                    # 线段退化为点
                    distance = ((x - x1) ** 2 + (y - y1) ** 2) ** 0.5
                else:
                    param = dot / len_sq

                    if param < 0:
                        xx, yy = x1, y1
                    elif param > 1:
                        xx, yy = x2, y2
                    else:
                        xx = x1 + param * C
                        yy = y1 + param * D

                    distance = ((x - xx) ** 2 + (y - yy) ** 2) ** 0.5

                min_distance = min(min_distance, distance)

            return min_distance <= threshold

        except Exception as e:
            logger.error(f"点到线段距离计算失败: {e}")
            return False

    @classmethod
    async def _save_alert_record_async(
        cls,
        task_id: int,
        stream_id: int,
        frame,
        alert_result: Dict[str, Any],
        algorithm_id: str,
        alert_config: Dict[str, Any]
    ):
        """
        异步保存告警记录，避免 greenlet 上下文问题
        """
        import asyncio
        import concurrent.futures

        def _save_alert_record_sync():
            """同步版本的保存告警记录方法"""
            try:
                # 使用同步的数据库连接
                from config.get_scheduler import SessionLocal
                from module_alert.entity.do.alert_do import SurveillanceAlert

                with SessionLocal() as sync_db:
                    # 保存告警截图（带检测框的图片）
                    screenshot_path = cls._save_alert_screenshot_sync(
                        task_id, stream_id, frame, alert_result
                    )

                    # 获取告警配置
                    alert_params = alert_config.get('alert_params', {})

                    # 提取检测结果的置信度
                    confidence = 0.0
                    if alert_result and isinstance(alert_result, dict):
                        # 自定义格式的检测结果
                        details = alert_result.get('details', {})
                        detections = details.get('detections', [])
                        if detections:
                            # 取最高置信度
                            confidence = max([det.get('conf', 0) for det in detections])
                        else:
                            # 兼容其他格式
                            in_area_detections = details.get('in_area_detections', [])
                            line_crossing_detections = details.get('line_crossing_detections', [])
                            all_scores = ([d.get('score', 0) for d in in_area_detections] +
                                         [d.get('score', 0) for d in line_crossing_detections])
                            if all_scores:
                                confidence = max(all_scores)

                    # 构建告警消息 - 包含车辆统计和ID信息
                    alert_message = cls._build_alert_message(alert_result, algorithm_id, alert_params)

                    # 构建增强的bbox_info，包含车辆ID和计数信息
                    enhanced_bbox_info = cls._build_enhanced_bbox_info(alert_result)

                    alert_record = SurveillanceAlert(
                        task_id=task_id,
                        stream_id=stream_id,
                        alert_type=algorithm_id,
                        alert_level='1',  # 默认告警级别
                        alert_message=alert_message,
                        alert_time=datetime.now(),
                        screenshot_path=screenshot_path,
                        bbox_info=json.dumps(enhanced_bbox_info),  # 保存增强的告警结果
                        confidence=confidence,
                        status='0',  # 未处理
                        create_time=datetime.now(),
                        create_by='system'
                    )

                    # 保存到数据库
                    sync_db.add(alert_record)
                    sync_db.commit()
                    logger.info(f"保存告警记录成功: 任务{task_id}")

                    # 更新任务告警次数
                    try:
                        cls._update_task_alert_count_sync(sync_db, task_id)
                    except Exception as update_error:
                        logger.error(f"更新任务告警次数失败，但告警记录已保存: task_id={task_id}, 错误: {update_error}")

            except Exception as e:
                logger.error(f"保存告警记录失败: task_id={task_id}, 错误: {e}")
                raise

        # 使用线程池执行器运行同步代码
        loop = asyncio.get_event_loop()
        with concurrent.futures.ThreadPoolExecutor() as executor:
            await loop.run_in_executor(executor, _save_alert_record_sync)

    @classmethod
    def _build_alert_message(cls, alert_result: Dict[str, Any], algorithm_id: str, alert_params: Dict[str, Any]) -> str:
        """
        构建告警消息，包含车辆统计和ID信息
        
        :param alert_result: 算法包返回的告警结果
        :param algorithm_id: 算法ID
        :param alert_params: 告警参数
        :return: 格式化的告警消息
        """
        try:
            # 优先使用数据库配置的alert_message作为基础消息
            base_message = alert_params.get('alert_message', '')
            if not base_message:
                base_message = f'{algorithm_id}告警'
            
            # 提取告警结果详情
            details = alert_result.get('details', {})
            
            # 获取真正触发告警的车辆ID（不是所有检测到的车辆）
            alert_vehicle_ids = []
            alert_vehicle_count = 0
            
            # 从后处理模块获取告警车辆ID
            detections = details.get('detections', [])
            for detection in detections:
                # 只有有告警原因的检测才是告警车辆
                if detection.get('alert_reason'):
                    track_id = detection.get('track_id')
                    if track_id is not None:
                        alert_vehicle_ids.append(track_id)
            alert_vehicle_count = len(alert_vehicle_ids)
            logger.info(f"从后处理模块获取告警车辆ID: {alert_vehicle_ids}")
            
            # 去重并排序
            unique_alert_vehicle_ids = list(set(alert_vehicle_ids))
            unique_alert_vehicle_ids.sort()
            alert_vehicle_count = len(unique_alert_vehicle_ids)
            
            # 构建告警消息 - 只显示车辆统计信息
            if alert_vehicle_count > 0:
                if len(unique_alert_vehicle_ids) == 1:
                    # 只有一个告警车辆ID，直接显示
                    message = f"告警车辆ID: {unique_alert_vehicle_ids[0]}"
                else:
                    # 多个告警车辆ID，显示前几个和总数
                    display_ids = unique_alert_vehicle_ids[:3]
                    message = f"告警车辆ID: {', '.join(map(str, display_ids))} 等{alert_vehicle_count}个"
            else:
                # 没有告警车辆ID信息，显示总数
                message = f"告警车辆数: {alert_vehicle_count}"
            
            logger.info(f"构建告警消息: {message}, 告警车辆数: {alert_vehicle_count}, 告警车辆ID: {unique_alert_vehicle_ids}")
            return message
            
        except Exception as e:
            logger.error(f"构建告警消息失败: {e}")
            # 返回基础消息作为后备
            return alert_params.get('alert_message', f'{algorithm_id}告警')

    @classmethod
    def _build_enhanced_bbox_info(cls, alert_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        构建增强的bbox_info，包含车辆ID、计数信息等

        :param alert_result: 算法包返回的告警结果
        :return: 增强的bbox_info
        """
        try:
            enhanced_info = {
                'original_result': alert_result,  # 保留原始结果
                'vehicle_tracking': {
                    'vehicle_ids': [],  # 所有车辆跟踪ID列表
                    'vehicle_count': 0,  # 当前检测到的车辆总数
                    'unique_vehicle_count': 0,  # 唯一车辆数（基于track_id去重）
                    'alert_vehicle_ids': [],  # 告警车辆ID列表
                    'unique_alert_vehicle_ids': [],  # 唯一告警车辆ID列表
                    'alert_vehicle_count': 0,  # 告警车辆数
                },
                'counting_statistics': {
                    'total_alert_count': 0,  # 总告警计数
                    'area_detection_count': 0,  # 区域检测计数
                    'line_crossing_count': 0,  # 线段穿越计数
                    'alert_threshold': 0,  # 告警阈值
                },
                'detection_details': {
                    'detections': [],  # 检测详情
                    'alert_messages': [],  # 告警消息列表
                }
            }

            # 提取details信息
            details = alert_result.get('details', {})

            # 提取计数统计信息
            enhanced_info['counting_statistics'] = {
                'total_alert_count': details.get('total_alert_count', 0),
                'area_detection_count': details.get('area_detection_count', 0),
                'line_crossing_count': details.get('total_line_count', 0),
                'alert_threshold': details.get('alert_threshold', 0),
            }

            # 提取检测详情和车辆跟踪信息
            detections = details.get('detections', [])

            # 优先使用后处理器提供的车辆跟踪信息
            vehicle_tracking_info = details.get('vehicle_tracking', {})
            if vehicle_tracking_info:
                # 使用后处理器生成的车辆跟踪信息
                enhanced_info['vehicle_tracking'] = {
                    'vehicle_ids': vehicle_tracking_info.get('vehicle_ids', []),
                    'unique_vehicle_ids': vehicle_tracking_info.get('unique_vehicle_ids', []),
                    'vehicle_count': vehicle_tracking_info.get('vehicle_count', 0),
                    'unique_vehicle_count': vehicle_tracking_info.get('unique_vehicle_count', 0),
                    'alert_vehicle_ids': vehicle_tracking_info.get('alert_vehicle_ids', []),
                    'unique_alert_vehicle_ids': vehicle_tracking_info.get('unique_alert_vehicle_ids', []),
                    'alert_vehicle_count': vehicle_tracking_info.get('alert_vehicle_count', 0),
                }
                logger.info(f"使用后处理器车辆跟踪信息: 车辆数={vehicle_tracking_info.get('vehicle_count', 0)}, 唯一车辆数={vehicle_tracking_info.get('unique_vehicle_count', 0)}")
                logger.info(f"车辆ID列表: {vehicle_tracking_info.get('vehicle_ids', [])}")
                logger.info(f"告警车辆ID列表: {vehicle_tracking_info.get('unique_alert_vehicle_ids', [])}, 告警车辆数: {vehicle_tracking_info.get('alert_vehicle_count', 0)}")
            else:
                # 兼容旧版本：从检测结果中提取车辆ID
                vehicle_ids = []
                unique_track_ids = set()

                for detection in detections:
                    # 提取车辆跟踪ID
                    track_id = detection.get('track_id')
                    if track_id is not None:
                        vehicle_ids.append(track_id)
                        unique_track_ids.add(track_id)

                # 更新车辆跟踪信息
                enhanced_info['vehicle_tracking'] = {
                    'vehicle_ids': vehicle_ids,
                    'unique_vehicle_ids': list(unique_track_ids),
                    'vehicle_count': len(detections),
                    'unique_vehicle_count': len(unique_track_ids),
                    'alert_vehicle_ids': vehicle_ids,  # 兼容旧版本，假设所有检测都是告警
                    'unique_alert_vehicle_ids': list(unique_track_ids),
                    'alert_vehicle_count': len(unique_track_ids),
                }
                logger.info(f"从检测结果提取车辆跟踪信息: 车辆数={len(detections)}, 唯一车辆数={len(unique_track_ids)}")

            # 保存检测详情（简化版本，避免数据过大）
            for detection in detections:
                enhanced_info['detection_details']['detections'].append({
                    'track_id': detection.get('track_id'),
                    'label': detection.get('label'),
                    'name': detection.get('name'),
                    'ch_name': detection.get('ch_name'),
                    'confidence': detection.get('conf'),
                    'bbox': detection.get('xyxy', []),
                    'color': detection.get('color'),
                    'alert_reason': detection.get('alert_reason')
                })

            # 提取告警消息
            enhanced_info['detection_details']['alert_messages'] = details.get('alert_messages', [])

            # 添加时间戳
            enhanced_info['timestamp'] = details.get('timestamp', datetime.now().isoformat())

            # 获取告警车辆信息用于日志
            alert_vehicle_count = enhanced_info['vehicle_tracking'].get('alert_vehicle_count', 0)
            alert_vehicle_ids = enhanced_info['vehicle_tracking'].get('unique_alert_vehicle_ids', [])

            logger.debug(f"构建增强bbox_info成功: 车辆数={len(detections)}, 唯一车辆数={enhanced_info['vehicle_tracking'].get('unique_vehicle_count', 0)}, 总计数={details.get('total_alert_count', 0)}")
            logger.info(f"🚗 告警车辆信息: 告警车辆数={alert_vehicle_count}, 告警车辆ID={alert_vehicle_ids}")

            return enhanced_info

        except Exception as e:
            logger.error(f"构建增强bbox_info失败: {e}")
            # 如果构建失败，返回原始结果
            return alert_result

    # 注释：_assign_vehicle_tracking_ids方法已移除
    # 现在完全由后处理器负责生成基于时间和计数的车辆ID
    # 这样确保了ID的一致性和合理性，避免了后端和后处理器的重复处理

    @classmethod
    async def _save_alert_record(
        cls,
        query_db: AsyncSession,
        task_info: SurveillanceTask,
        stream_info: SurveillanceStream,
        frame,
        alert_result: Dict[str, Any]
    ):
        """
        保存告警记录和检测图片
        """
        try:
            # 保存告警截图（带检测框的图片）
            screenshot_path = await cls._save_alert_screenshot(
                task_info.task_id, stream_info.stream_id, frame, alert_result
            )

            # 获取告警配置
            alert_config = task_info.alert_config or {}
            alert_params = alert_config.get('alert_params', {})

            # 创建告警记录
            from module_alert.entity.do.alert_do import SurveillanceAlert

            # 提取检测结果的置信度
            confidence = 0.0
            if alert_result and isinstance(alert_result, dict):
                # 自定义格式的检测结果
                details = alert_result.get('details', {})
                detections = details.get('detections', [])
                if detections:
                    # 取最高置信度
                    confidence = max([det.get('conf', 0) for det in detections])
                else:
                    # 兼容其他格式
                    in_area_detections = details.get('in_area_detections', [])
                    line_crossing_detections = details.get('line_crossing_detections', [])
                    all_scores = ([d.get('score', 0) for d in in_area_detections] +
                                 [d.get('score', 0) for d in line_crossing_detections])
                    if all_scores:
                        confidence = max(all_scores)

            # 构建告警消息 - 包含车辆统计和ID信息
            alert_message = cls._build_alert_message(alert_result, task_info.algorithm_id, alert_params)

            # 构建增强的bbox_info，包含车辆ID和计数信息
            enhanced_bbox_info = cls._build_enhanced_bbox_info(alert_result)

            alert_record = SurveillanceAlert(
                task_id=task_info.task_id,
                stream_id=stream_info.stream_id,
                alert_type=task_info.algorithm_id,
                alert_level='1',  # 默认告警级别
                alert_message=alert_message,
                alert_time=datetime.now(),
                screenshot_path=screenshot_path,
                bbox_info=json.dumps(enhanced_bbox_info),  # 保存增强的告警结果
                confidence=confidence,
                status='0',  # 未处理
                create_time=datetime.now(),
                create_by='system'
            )

            # 保存到数据库
            query_db.add(alert_record)
            await query_db.commit()
            logger.info(f"保存告警记录成功: 任务{task_info.task_id}")

            # 更新任务告警次数 - 使用独立的事务
            try:
                await cls._update_task_alert_count(query_db, task_info.task_id)
            except Exception as update_error:
                logger.error(f"更新任务告警次数失败，但告警记录已保存: task_id={task_info.task_id}, 错误: {update_error}")

        except Exception as e:
            logger.error(f"保存告警记录失败: task_id={task_info.task_id}, 错误: {e}")
            await query_db.rollback()
            raise  # 重新抛出异常以便上层处理

    @classmethod
    async def _save_alert_screenshot(cls, task_id: int, stream_id: int, frame, alert_result: Dict[str, Any]) -> str:
        """
        保存告警截图（带检测框和识别框）
        存储路径：static/alert_screenshots/
        """
        try:
            # 使用static目录存储告警截图
            base_dir = Path("static/alert_screenshots")
            base_dir.mkdir(parents=True, exist_ok=True)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            filename = f"alert_{task_id}_{stream_id}_{timestamp}.jpg"
            screenshot_path = base_dir / filename

            # 创建帧副本以避免修改原始帧
            frame_copy = frame.copy()

            # 绘制检测框和识别框（在副本上绘制）
            # 使用优化的绘制方法确保一致性
            frame_height, frame_width = frame_copy.shape[:2]
            cls._draw_detection_boxes_optimized(frame_copy, alert_result, frame_width, frame_height)

            # 保存带检测框的截图
            success = cv2.imwrite(str(screenshot_path), frame_copy)

            if not success:
                logger.error(f"保存告警截图失败: cv2.imwrite返回False")
                raise Exception("cv2.imwrite failed")

            # 返回可访问的URL路径（用于数据库存储和前端访问）
            url_path = f"/static/alert_screenshots/{filename}"
            logger.info(f"告警截图保存成功: {screenshot_path}")
            logger.info(f"告警截图URL: {url_path}")

            return url_path

        except Exception as e:
            logger.error(f"保存告警截图失败: {e}")
            # 如果绘制失败，保存原始帧作为备用
            try:
                base_dir = Path("static/alert_screenshots")
                base_dir.mkdir(parents=True, exist_ok=True)

                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                filename = f"alert_{task_id}_{stream_id}_{timestamp}_original.jpg"
                screenshot_path = base_dir / filename
                success = cv2.imwrite(str(screenshot_path), frame)

                if not success:
                    logger.error(f"保存原始帧也失败: cv2.imwrite返回False")
                    return ""

                url_path = f"/static/alert_screenshots/{filename}"
                logger.warning(f"告警截图保存成功（原始帧）: {screenshot_path}")
                logger.warning(f"告警截图URL（原始帧）: {url_path}")

                return url_path
            except Exception as fallback_error:
                logger.error(f"保存原始帧也失败: {fallback_error}")
                return ""

    @classmethod
    async def _save_original_frame(cls, task_id: int, stream_id: int, frame) -> str:
        """
        保存原始图片（无检测框）
        """
        try:
            # 创建原始图片目录
            original_dir = Path("uploads/originals") / str(datetime.now().strftime("%Y%m%d"))
            original_dir.mkdir(parents=True, exist_ok=True)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            filename = f"original_{task_id}_{stream_id}_{timestamp}.jpg"
            original_path = original_dir / filename

            # 保存原始图片
            cv2.imwrite(str(original_path), frame)

            return str(original_path)

        except Exception as e:
            logger.error(f"保存原始图片失败: {e}")
            return ""

    @classmethod
    def _draw_detection_boxes(cls, frame, alert_result: Dict[str, Any]):
        """
        在帧上绘制检测目标的检测框、配置区域和线段（优化版）

        ==================== 核心功能说明 ====================
        此方法负责在视频帧上绘制：
        1. 只绘制在配置区域内的检测目标（避免无关检测框）
        2. 配置的检测区域（黄色半透明区域）
        3. 配置的检测线段（青色线段）

        ==================== 绘制逻辑（优化版） ====================
        1. 始终绘制配置区域和线段（作为监控范围指示）
        2. 检查每个检测目标是否在配置的区域内
        3. 只绘制在区域内的目标：
           - 告警状态：红色粗框(3px) + 告警信息
           - 正常状态：绿色细框(2px) + 类别名 + 置信度
        4. 区域外的目标不绘制检测框（减少视觉干扰）

        ==================== 视觉效果 ====================
        最终在视频帧上显示：
        - 🟡 黄色半透明区域：检测区域范围
        - 🔵 青色线段：检测线段
        - 🔴 红色粗框：区域内告警目标
        - 🟢 绿色细框：区域内普通目标
        - ⚪ 白色文字：时间戳和监控状态
        """
        try:
            logger.debug("开始绘制检测框和配置区域")

            # 验证输入参数
            if frame is None:
                logger.error("绘制失败: frame为None")
                return

            if not isinstance(alert_result, dict):
                logger.warning(f"告警结果格式异常: {type(alert_result)}, 使用空字典")
                alert_result = {}

            # 创建帧的副本，避免在原始帧上累积绘制导致框遗留问题
            frame_copy = frame.copy()

            details = alert_result.get('details', {}) if alert_result else {}

            # 添加调试日志
            if alert_result:
                logger.debug(f"告警结果结构: hit={alert_result.get('hit')}, message={alert_result.get('message')}")
                logger.debug(f"Alert result keys: {list(alert_result.keys())}")
                logger.debug(f"Details keys: {list(details.keys())}")

                if 'alert_targets' in details:
                    logger.debug(f"告警目标数量: {len(details['alert_targets'])}")
                if 'configured_areas' in details:
                    logger.debug(f"配置区域数量: {len(details['configured_areas'])}")
                if 'configured_lines' in details:
                    logger.debug(f"配置线段数量: {len(details['configured_lines'])}")

            # 始终绘制配置的区域和线段（作为监控范围显示）
            cls._draw_configured_areas_and_lines(frame_copy, alert_result or {})

            # ==================== 绘制检测目标（算法包已过滤） ====================
            #
            # 优化逻辑：算法包已经过滤了区域内的检测目标，这里直接绘制即可
            #
            detections = details.get('detections', [])
            logger.debug(f"开始处理检测结果: 总数{len(detections)}个（算法包已过滤区域内目标）")

            if detections:
                alert_count = 0
                normal_count = 0
                skipped_count = 0

                for detection in detections:
                    # 获取检测框坐标
                    xyxy = detection.get('xyxy', detection.get('bbox', detection.get('box', [])))
                    if len(xyxy) < 4:
                        logger.debug(f"跳过无效检测框: {detection}")
                        skipped_count += 1
                        continue

                    # 获取算法包建议的颜色（RGB格式，可选字段）
                    provided_color = detection.get('color', None)
                    logger.debug(f"检测目标颜色信息: track_id={detection.get('track_id')}, provided_color={provided_color}")

                    # 检查是否有告警原因（由算法包设置）
                    has_alert_reason = bool(detection.get('alert_reason'))

                    if has_alert_reason:
                        # 有告警原因：绘制告警样式的检测框
                        alert_reason = detection.get('alert_reason', '告警')
                        logger.debug(f"绘制告警目标: track_id={detection.get('track_id')}, reason={alert_reason}")
                        cls._draw_single_detection_as_alert(frame_copy, detection, provided_color)
                        alert_count += 1
                    else:
                        # 无告警原因：绘制普通样式的检测框
                        logger.debug(f"绘制普通目标: track_id={detection.get('track_id')}")
                        cls._draw_single_detection_as_normal(frame_copy, detection, provided_color)
                        normal_count += 1

                # ==================== 绘制结果统计 ====================
                logger.info(f"绘制检测结果完成: 告警目标{alert_count}个, 普通目标{normal_count}个, 跳过{skipped_count}个")
                logger.debug(f"绘制详情: 总检测数={len(detections)}, 告警数={alert_count}, 普通数={normal_count}, 跳过数={skipped_count}")

                # 验证绘制完整性
                total_processed = alert_count + normal_count + skipped_count
                if total_processed != len(detections):
                    logger.warning(f"处理数量不匹配: 检测总数={len(detections)}, 已处理={total_processed}")
            else:
                # 如果没有detections，则查找其他检测结果
                # 优先绘制告警目标（经过后处理判断的目标）
                alert_targets = details.get('alert_targets', [])
                if alert_targets:
                    logger.debug(f"绘制告警目标: {len(alert_targets)}个")
                    for target in alert_targets:
                        cls._draw_single_alert_target(frame_copy, target)
                else:
                    # 绘制区域内检测（告警目标）
                    in_area_detections = details.get('in_area_detections', [])
                    for detection in in_area_detections:
                        cls._draw_area_detection(frame_copy, detection)

                    # 绘制线段检测（告警目标）
                    line_crossing_detections = details.get('line_crossing_detections', [])
                    for detection in line_crossing_detections:
                        cls._draw_line_detection(frame_copy, detection)

                    # 如果以上都没有，则从data.bbox中获取告警目标
                    data = alert_result.get('data', {})
                    bbox_data = data.get('bbox', {})
                    rectangles = bbox_data.get('rectangles', [])
                    if rectangles:
                        logger.debug(f"绘制bbox矩形: {len(rectangles)}个")
                        for rect in rectangles:
                            if len(rect) >= 4:
                                x1, y1, x2, y2 = map(int, rect)
                                # 绘制红色告警框
                                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 0, 255), 3)
                                # 添加告警标签
                                frame = cls._put_chinese_text(frame, "告警目标", (x1, y1-25),
                                                            font_size=16, color=(0, 0, 255))

            # 显示监控状态
            if alert_result and alert_result.get('hit', False):
                cv2.putText(frame, "ALERT DETECTED", (10, 60),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
            else:
                cv2.putText(frame, "Monitoring - No Alert", (10, 60),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)

            # 添加时间戳
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            cv2.putText(frame, timestamp, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)

            # 添加告警信息
            message = alert_result.get('message', '')
            if message:
                # 分行显示长消息
                lines = message.split(';')
                for i, line in enumerate(lines[:3]):  # 最多显示3行
                    if line.strip():
                        frame = cls._put_chinese_text(frame, line.strip(), (10, 60 + i*25), font_size=18, color=(0, 255, 255))

            # 移除检测统计显示，保持画面简洁
            # 统计数据仅用于日志记录
            total_detections = details.get('total_detections', len(detections))
            # 使用前面计算的统计数据
            if 'alert_count' not in locals():
                alert_count = 0
            if 'normal_count' not in locals():
                normal_count = 0
            if 'skipped_count' not in locals():
                skipped_count = 0

            # 不再在画面上显示统计信息，保持界面简洁
            # stats_text = f"Total: {total_detections}, Alerts: {alert_count}"
            # cv2.putText(frame_copy, stats_text, (10, frame_copy.shape[0] - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

            # 添加调试日志
            logger.info(f"绘制完成: 告警目标{alert_count}个, 普通目标{normal_count}个, 跳过{skipped_count}个")

        except Exception as e:
            logger.error(f"绘制检测框失败: {e}")

    @classmethod
    def _is_detection_in_configured_areas(
        cls,
        xyxy: List[int],
        configured_areas: List[Dict],
        frame_width: int,
        frame_height: int
    ) -> bool:
        """
        判断检测框是否在配置的区域内

        :param xyxy: 检测框坐标 [x1, y1, x2, y2]
        :param configured_areas: 配置的检测区域列表
        :param frame_width: 帧宽度
        :param frame_height: 帧高度
        :return: 是否在区域内
        """
        try:
            if not configured_areas:
                return True  # 没有配置区域时，认为所有检测都在区域内

            # 计算检测框中心点
            x1, y1, x2, y2 = xyxy
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2

            # 检查是否在任何一个配置区域内
            for area in configured_areas:
                points = area.get('points', [])
                if len(points) < 3:
                    continue

                # 转换点坐标
                polygon_points = []
                for point in points:
                    if isinstance(point, dict):
                        x, y = point.get('x', 0), point.get('y', 0)
                    elif isinstance(point, (list, tuple)) and len(point) >= 2:
                        x, y = point[0], point[1]
                    else:
                        continue

                    # 如果是相对坐标（0-1之间），转换为绝对坐标
                    if 0 <= x <= 1 and 0 <= y <= 1:
                        x = int(x * frame_width)
                        y = int(y * frame_height)
                    else:
                        x, y = int(x), int(y)

                    polygon_points.append((x, y))

                # 使用点在多边形判断
                if len(polygon_points) >= 3 and cls._point_in_polygon((center_x, center_y), polygon_points):
                    logger.debug(f"检测框中心点({center_x}, {center_y})在区域{area.get('name', 'unknown')}内")
                    return True

            logger.debug(f"检测框中心点({center_x}, {center_y})不在任何配置区域内")
            return False

        except Exception as e:
            logger.error(f"区域判断失败: {e}")
            return True  # 出错时默认绘制

    @classmethod
    def _draw_single_alert_target(cls, frame, target: Dict[str, Any]):
        """
        绘制单个告警目标
        """
        try:
            # 获取目标位置信息
            bbox = target.get('bbox', target.get('box', target.get('xyxy', [])))
            if len(bbox) >= 4:
                x1, y1, x2, y2 = map(int, bbox)

                # 颜色选择逻辑：优先使用算法包提供的颜色，否则根据告警类型选择默认颜色
                original_color = target.get('original_color')
                alert_type = target.get('alert_type', 'unknown')

                if original_color and isinstance(original_color, list) and len(original_color) == 3:
                    # 使用算法包提供的颜色（RGB转BGR）
                    r, g, b = original_color
                    color = (b, g, r)  # OpenCV使用BGR格式
                    label_prefix = "检测"
                else:
                    # 使用我们的默认颜色（根据告警类型）
                    if alert_type == 'area_intrusion':
                        color = (0, 0, 255)  # 红色 - 区域入侵
                        label_prefix = "区域入侵"
                    elif alert_type == 'line_crossing':
                        color = (0, 255, 255)  # 黄色 - 越线
                        label_prefix = "越线"
                    elif alert_type == 'vehicle_counting':
                        color = (255, 0, 255)  # 紫色 - 车辆计数
                        label_prefix = "车辆告警"
                    else:
                        color = (0, 0, 255)  # 默认红色
                        label_prefix = "告警"

                # 绘制告警框（加粗）
                cv2.rectangle(frame, (x1, y1), (x2, y2), color, 3)

                # 添加标签
                confidence = target.get('confidence', target.get('score', target.get('conf', 0)))
                if confidence is None:
                    confidence = 0.0

                label = f"{label_prefix} {confidence:.2f}"
                frame = cls._put_chinese_text(frame, label, (x1, y1-25), font_size=16, color=color)

                # 添加告警原因
                alert_reason = target.get('alert_reason', target.get('reason', ''))
                if alert_reason:
                    frame = cls._put_chinese_text(frame, alert_reason, (x1, y2+10), font_size=14, color=color)

        except Exception as e:
            logger.error(f"绘制告警目标失败: {e}")

    @classmethod
    def _draw_single_detection_as_alert(cls, frame, detection: Dict[str, Any], provided_color=None):
        """
        绘制单个检测结果作为告警目标（触发告警的检测对象）

        ==================== 告警目标特征 ====================
        告警目标是指经过算法包后处理判断后，触发了告警条件的检测对象，例如：
        - 车辆进入了禁止区域
        - 人员跨越了警戒线
        - 检测数量超过了阈值

        ==================== 输入数据格式 ====================
        detection 参数的数据结构：
        {
            'label': 2,                           # 类别ID
            'conf': 0.86,                         # 置信度 [0-1]
            'name': 'car',                        # 英文类别名
            'ch_name': 'car',                     # 中文类别名
            'xyxy': [704, 194, 784, 253],         # 检测框坐标 [x1, y1, x2, y2]
            'track_id': 12,                       # 跟踪ID（可选）
            'color': [255, 0, 0],                 # 算法包建议颜色RGB（可选）
            'alert_reason': '车辆进入区域1'        # 告警原因（告警目标特有字段）
        }

        ==================== 绘制规则 ====================
        1. 检测框：粗线条（3像素）以突出告警状态
        2. 颜色优先级：
           - 优先使用算法包提供的 provided_color（RGB格式）
           - 如果没有提供，使用默认红色 (0, 0, 255) BGR格式
        3. 标签显示：
           - 主标签：类别名 + 置信度（如："car 0.86"）
           - 副标签：告警原因（如："车辆进入区域1"）
        4. 位置：主标签在框上方，副标签在框下方

        :param frame: OpenCV图像帧（BGR格式）
        :param detection: 检测结果字典
        :param provided_color: 算法包提供的颜色RGB格式，如果为None则使用默认红色
        """
        try:
            # 获取检测框坐标
            xyxy = detection.get('xyxy', detection.get('bbox', detection.get('box', [])))
            if len(xyxy) >= 4:
                x1, y1, x2, y2 = map(int, xyxy)

                # 确定绘制颜色：优先使用算法包提供的颜色，否则使用默认红色
                if provided_color and isinstance(provided_color, list) and len(provided_color) >= 3:
                    # 算法包提供的颜色格式是RGB，需要转换为BGR（OpenCV格式）
                    draw_color = (provided_color[2], provided_color[1], provided_color[0])  # RGB -> BGR
                    text_color = draw_color
                    logger.debug(f"使用算法包提供的告警颜色: RGB{provided_color} -> BGR{draw_color}")
                else:
                    # 默认红色（BGR格式）
                    draw_color = (0, 0, 255)
                    text_color = (0, 0, 255)
                    logger.debug("使用默认告警颜色: 红色")

                # 绘制告警框
                cv2.rectangle(frame, (x1, y1), (x2, y2), draw_color, 3)

                # 获取标签信息
                name = detection.get('ch_name', detection.get('name', '目标'))
                conf = detection.get('conf', detection.get('confidence', 0))
                track_id = detection.get('track_id', None)
                alert_reason = detection.get('alert_reason', '告警')

                # 调试日志：检查数据是否正确
                logger.debug(f"告警检测框绘制 - name: {name}, conf: {conf}, track_id: {track_id}, alert_reason: {alert_reason}")

                # 添加标签（包含track_id以便识别重复计数）
                if track_id is not None:
                    label = f"{name} {conf:.2f} ID:{track_id}"
                else:
                    label = f"{name} {conf:.2f}"

                logger.debug(f"告警检测框标签: {label}, 位置: ({x1}, {y1-25})")
                frame = cls._put_chinese_text(frame, label, (x1, y1-25), font_size=16, color=text_color)

                # 添加告警原因
                if alert_reason:
                    frame = cls._put_chinese_text(frame, alert_reason, (x1, y2+10), font_size=14, color=text_color)

        except Exception as e:
            logger.error(f"绘制告警检测失败: {e}")

    @classmethod
    def _draw_single_detection_as_normal(cls, frame, detection: Dict[str, Any], provided_color=None):
        """
        绘制单个检测结果作为普通目标（正常检测到的对象）

        ==================== 普通目标特征 ====================
        普通目标是指被AI模型检测到，但未触发任何告警条件的对象，例如：
        - 在允许区域内正常行驶的车辆
        - 未跨越警戒线的人员
        - 数量在正常范围内的检测对象

        ==================== 输入数据格式 ====================
        detection 参数的数据结构：
        {
            'label': 2,                           # 类别ID
            'conf': 0.91,                         # 置信度 [0-1]
            'name': 'car',                        # 英文类别名
            'ch_name': 'car',                     # 中文类别名
            'xyxy': [396, 478, 543, 627],         # 检测框坐标 [x1, y1, x2, y2]
            'track_id': 0,                        # 跟踪ID（可选）
            'color': [0, 255, 0]                  # 算法包建议颜色RGB（可选）
            # 注意：普通目标没有 'alert_reason' 字段
        }

        ==================== 绘制规则 ====================
        1. 检测框：细线条（2像素）表示正常状态
        2. 颜色优先级：
           - 优先使用算法包提供的 provided_color（RGB格式）
           - 如果没有提供，使用默认绿色 (0, 255, 0) BGR格式
        3. 标签显示：
           - 只显示：类别名 + 置信度（如："car 0.91"）
           - 不显示告警原因（因为没有告警）
        4. 位置：标签在检测框上方
        5. 字体：使用OpenCV内置字体，较小尺寸以避免遮挡

        :param frame: OpenCV图像帧（BGR格式）
        :param detection: 检测结果字典
        :param provided_color: 算法包提供的颜色RGB格式，如果为None则使用默认绿色
        """
        try:
            # 获取检测框坐标
            xyxy = detection.get('xyxy', detection.get('bbox', detection.get('box', [])))
            if len(xyxy) >= 4:
                x1, y1, x2, y2 = map(int, xyxy)

                # 确定绘制颜色：优先使用算法包提供的颜色，否则使用默认绿色
                if provided_color and isinstance(provided_color, list) and len(provided_color) >= 3:
                    # 算法包提供的颜色格式通常是RGB，需要转换为BGR（OpenCV格式）
                    draw_color = (provided_color[2], provided_color[1], provided_color[0])  # RGB -> BGR
                    text_color = draw_color
                    logger.debug(f"使用算法包提供的普通颜色: RGB{provided_color} -> BGR{draw_color}")
                else:
                    # 默认绿色（BGR格式）
                    draw_color = (0, 255, 0)
                    text_color = (0, 255, 0)
                    logger.debug("使用默认普通颜色: 绿色")

                # 绘制检测框
                cv2.rectangle(frame, (x1, y1), (x2, y2), draw_color, 2)

                # 获取标签信息
                name = detection.get('ch_name', detection.get('name', '目标'))
                conf = detection.get('conf', detection.get('confidence', 0))
                track_id = detection.get('track_id', None)

                # 调试日志：检查数据是否正确
                logger.debug(f"普通检测框绘制 - name: {name}, conf: {conf}, track_id: {track_id}")

                # 添加标签（包含track_id以便识别重复计数）
                if track_id is not None:
                    label = f"{name} {conf:.2f} ID:{track_id}"
                else:
                    label = f"{name} {conf:.2f}"

                logger.debug(f"普通检测框标签: {label}, 位置: ({x1}, {y1-25})")
                frame = cls._put_chinese_text(frame, label, (x1, y1-25), font_size=16, color=text_color)

        except Exception as e:
            logger.error(f"绘制普通检测失败: {e}")

    @classmethod
    def _draw_area_detection(cls, frame, detection: Dict[str, Any]):
        """
        绘制区域内检测（告警目标）
        """
        try:
            box = detection.get('box', detection.get('bbox', []))
            if len(box) >= 4:
                x1, y1, x2, y2 = map(int, box)
                # 绘制红色检测框（区域内检测）
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 0, 255), 3)
                # 添加标签
                area_name = detection.get('area_name', '区域')
                score = detection.get('score', detection.get('confidence', 0))
                if score is None:
                    score = 0.0
                label = f"{area_name} {score:.2f}"
                frame = cls._put_chinese_text(frame, label, (x1, y1-25), font_size=16, color=(0, 0, 255))
        except Exception as e:
            logger.error(f"绘制区域检测失败: {e}")

    @classmethod
    def _draw_line_detection(cls, frame, detection: Dict[str, Any]):
        """
        绘制线段检测（告警目标）
        """
        try:
            box = detection.get('box', detection.get('bbox', []))
            if len(box) >= 4:
                x1, y1, x2, y2 = map(int, box)
                # 绘制黄色检测框（线段检测）
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 255), 3)
                # 添加标签
                line_name = detection.get('line_name', '线段')
                score = detection.get('score', detection.get('confidence', 0))
                if score is None:
                    score = 0.0
                label = f"{line_name} {score:.2f}"
                frame = cls._put_chinese_text(frame, label, (x1, y1-25), font_size=16, color=(0, 255, 255))
        except Exception as e:
            logger.error(f"绘制线段检测失败: {e}")

    @classmethod
    def _draw_configured_areas_and_lines_stable(cls, frame, alert_result: Dict[str, Any]):
        """
        稳定的区域和线段绘制函数，避免闪烁
        """
        try:
            details = alert_result.get('details', {})

            # 优先使用配置的区域信息
            configured_areas = details.get('configured_areas', [])
            configured_lines = details.get('configured_lines', [])

            # 如果没有配置区域，尝试从算法返回的多边形区域获取
            polygons = details.get('polygons', {})

            # 合并所有区域，确保稳定显示
            all_areas = list(configured_areas)
            if not all_areas and polygons:
                # 只有在没有配置区域时才使用算法返回的区域
                for polygon_name, polygon_data in polygons.items():
                    if isinstance(polygon_data, dict) and 'points' in polygon_data:
                        all_areas.append({
                            'name': polygon_name,
                            'points': polygon_data['points']
                        })

            # 绘制区域
            cls._draw_areas_stable(frame, all_areas)

            # 绘制线段
            cls._draw_lines_stable(frame, configured_lines)

        except Exception as e:
            logger.error(f"稳定绘制配置区域和线段失败: {e}")

    @classmethod
    def _draw_areas_stable(cls, frame, areas):
        """
        稳定绘制区域，避免闪烁
        """
        try:
            for area in areas:
                area_name = area.get('name', '区域')
                points = area.get('points', [])

                if len(points) >= 3:  # 至少需要3个点构成区域
                    # 转换点格式，处理相对坐标和绝对坐标
                    pts = []
                    frame_height, frame_width = frame.shape[:2]

                    for point in points:
                        if isinstance(point, dict):
                            x, y = point.get('x', 0), point.get('y', 0)
                        elif isinstance(point, (list, tuple)) and len(point) >= 2:
                            x, y = point[0], point[1]
                        else:
                            continue

                        # 如果是相对坐标（0-1之间），转换为绝对坐标
                        if 0 <= x <= 1 and 0 <= y <= 1:
                            x = int(x * frame_width)
                            y = int(y * frame_height)
                        else:
                            x, y = int(x), int(y)

                        pts.append([x, y])

                    if len(pts) >= 3:
                        import numpy as np
                        pts_array = np.array(pts, np.int32)
                        pts_array = pts_array.reshape((-1, 1, 2))

                        # 绘制半透明区域
                        overlay = frame.copy()
                        cv2.fillPoly(overlay, [pts_array], (255, 255, 0))  # 黄色区域
                        cv2.addWeighted(overlay, 0.3, frame, 0.7, 0, frame)

                        # 绘制区域边界
                        cv2.polylines(frame, [pts_array], True, (0, 255, 255), 2)  # 青色边界

                        # 添加区域名称
                        if pts:
                            center_x = sum(p[0] for p in pts) // len(pts)
                            center_y = sum(p[1] for p in pts) // len(pts)
                            frame = cls._put_chinese_text(frame, area_name, (center_x-20, center_y), font_size=18, color=(0, 255, 255))

                        logger.debug(f"绘制区域: {area_name}, 点数: {len(pts)}, 坐标: {pts[:3]}...")

        except Exception as e:
            logger.error(f"稳定绘制区域失败: {e}")

    @classmethod
    def _draw_lines_stable(cls, frame, lines):
        """
        稳定绘制线段，避免闪烁
        """
        try:
            for line in lines:
                line_name = line.get('name', '线段')
                start_point = line.get('start_point', {})
                end_point = line.get('end_point', {})

                if start_point and end_point:
                    frame_height, frame_width = frame.shape[:2]

                    x1, y1 = start_point.get('x', 0), start_point.get('y', 0)
                    x2, y2 = end_point.get('x', 0), end_point.get('y', 0)

                    # 如果是相对坐标（0-1之间），转换为绝对坐标
                    if 0 <= x1 <= 1 and 0 <= y1 <= 1:
                        x1 = int(x1 * frame_width)
                        y1 = int(y1 * frame_height)
                    else:
                        x1, y1 = int(x1), int(y1)

                    if 0 <= x2 <= 1 and 0 <= y2 <= 1:
                        x2 = int(x2 * frame_width)
                        y2 = int(y2 * frame_height)
                    else:
                        x2, y2 = int(x2), int(y2)

                    # 绘制线段
                    cv2.line(frame, (x1, y1), (x2, y2), (255, 0, 255), 3)  # 紫色线段

                    # 添加线段名称
                    mid_x = (x1 + x2) // 2
                    mid_y = (y1 + y2) // 2
                    frame = cls._put_chinese_text(frame, line_name, (mid_x-20, mid_y-10), font_size=18, color=(255, 0, 255))

                    logger.debug(f"绘制线段: {line_name}, 坐标: ({x1},{y1}) -> ({x2},{y2})")

        except Exception as e:
            logger.error(f"稳定绘制线段失败: {e}")

    @classmethod
    def _draw_configured_areas_and_lines(cls, frame, alert_result: Dict[str, Any]):
        """
        原始的区域和线段绘制函数（保留兼容性）
        """
        # 调用新的稳定绘制函数
        cls._draw_configured_areas_and_lines_stable(frame, alert_result)

    @classmethod
    async def _update_task_status(cls, query_db: AsyncSession, task_id: int, status: str):
        """
        更新任务状态
        """
        try:
            stmt = (
                update(SurveillanceTask)
                .where(SurveillanceTask.task_id == task_id)
                .values(
                    status=status,
                    last_run_time=datetime.now() if status == '1' else SurveillanceTask.last_run_time,
                    update_time=datetime.now()
                )
            )
            await query_db.execute(stmt)
            await query_db.commit()

        except Exception as e:
            logger.error(f"更新任务状态失败: {e}")
            await query_db.rollback()

    @classmethod
    async def _update_task_status_with_error(cls, query_db: AsyncSession, task_id: int, status: str, error_msg: str):
        """
        更新任务状态并记录错误信息
        """
        try:
            stmt = (
                update(SurveillanceTask)
                .where(SurveillanceTask.task_id == task_id)
                .values(
                    status=status,
                    last_run_time=datetime.now() if status == '1' else SurveillanceTask.last_run_time,
                    update_time=datetime.now(),
                    remark=error_msg  # 将错误信息记录到备注字段
                )
            )
            await query_db.execute(stmt)
            await query_db.commit()
            logger.info(f"任务 {task_id} 状态更新为 {status}，错误信息: {error_msg}")

        except Exception as e:
            logger.error(f"更新任务状态失败: {e}")
            await query_db.rollback()

    @classmethod
    async def _update_task_run_count(cls, query_db: AsyncSession, task_id: int):
        """
        更新任务运行次数
        """
        try:
            stmt = (
                update(SurveillanceTask)
                .where(SurveillanceTask.task_id == task_id)
                .values(
                    run_count=SurveillanceTask.run_count + 1,
                    update_time=datetime.now()
                )
            )
            await query_db.execute(stmt)
            await query_db.commit()

        except Exception as e:
            logger.error(f"更新任务运行次数失败: {e}")

    @classmethod
    def _save_alert_screenshot_sync(cls, task_id: int, stream_id: int, frame, alert_result: Dict[str, Any]) -> str:
        """
        同步版本的保存告警截图方法（带检测框和区域绘制）
        """
        try:
            # 创建保存目录
            save_dir = Path("static/alert_screenshots")
            save_dir.mkdir(parents=True, exist_ok=True)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            filename = f"alert_{task_id}_{stream_id}_{timestamp}.jpg"
            file_path = save_dir / filename

            # ✅ 复制帧以避免修改原始帧
            annotated_frame = frame.copy()

            # ✅ 绘制检测框和识别区域（关键修复）
            frame_height, frame_width = annotated_frame.shape[:2]
            cls._draw_detection_boxes_optimized(annotated_frame, alert_result, frame_width, frame_height)

            # ✅ 保存带检测框的图像
            success = cv2.imwrite(str(file_path), annotated_frame)
            if not success:
                logger.error(f"保存告警截图失败: cv2.imwrite返回False")
                raise Exception("cv2.imwrite failed")

            # 返回URL路径
            url_path = f"/static/alert_screenshots/{filename}"
            logger.info(f"告警截图保存成功: {file_path}")
            logger.info(f"告警截图URL: {url_path}")

            return url_path

        except Exception as e:
            logger.error(f"保存告警截图失败: {e}")
            # 如果绘制失败，保存原始帧作为备用
            try:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                filename = f"alert_{task_id}_{stream_id}_{timestamp}_original.jpg"
                file_path = save_dir / filename
                cv2.imwrite(str(file_path), frame)

                url_path = f"/static/alert_screenshots/{filename}"
                logger.warning(f"告警截图保存成功（原始帧）: {file_path}")
                return url_path
            except Exception as fallback_error:
                logger.error(f"保存原始帧也失败: {fallback_error}")
                return ""

    @classmethod
    def _update_task_alert_count_sync(cls, sync_db, task_id: int):
        """
        同步版本的更新任务告警次数方法
        """
        try:
            from sqlalchemy import text

            # 使用原生SQL更新，避免ORM的复杂性
            sql = text("""
                UPDATE surveillance_task
                SET alert_count = alert_count + 1,
                    update_time = :update_time
                WHERE task_id = :task_id
            """)

            sync_db.execute(sql, {
                'update_time': datetime.now(),
                'task_id': task_id
            })
            sync_db.commit()
            logger.debug(f"成功更新任务告警次数: task_id={task_id}")

        except Exception as e:
            logger.error(f"更新任务告警次数失败: task_id={task_id}, 错误: {e}")
            sync_db.rollback()

    @classmethod
    async def _update_task_alert_count(cls, query_db: AsyncSession, task_id: int):
        """
        更新任务告警次数
        """
        try:
            stmt = (
                update(SurveillanceTask)
                .where(SurveillanceTask.task_id == task_id)
                .values(
                    alert_count=SurveillanceTask.alert_count + 1,
                    update_time=datetime.now()
                )
            )
            await query_db.execute(stmt)
            await query_db.commit()
            logger.debug(f"成功更新任务告警次数: task_id={task_id}")

        except Exception as e:
            logger.error(f"更新任务告警次数失败: task_id={task_id}, 错误: {e}")
            await query_db.rollback()

    @classmethod
    def get_running_tasks(cls) -> Dict[int, Dict[str, Any]]:
        """
        获取运行中的任务列表
        """
        return cls.running_tasks.copy()

    @classmethod
    async def _cleanup_task_resources(cls, task_id: int):
        """
        清理任务相关资源

        :param task_id: 任务ID
        """
        try:
            # 清理异步任务
            if task_id in cls.running_tasks:
                task_process = cls.running_tasks[task_id]['process']
                if task_process and not task_process.done():
                    task_process.cancel()
                del cls.running_tasks[task_id]
                logger.info(f"已清理异步任务: {task_id}")

            # 清理其他资源（原算法执行服务已移除，因为使用智驱力直接集成）
            logger.info(f"任务 {task_id} 使用智驱力直接集成，无需清理外部进程")

            # 清理监控流
            try:
                cls.stop_monitor_stream(task_id)
            except Exception as e:
                logger.warning(f"清理监控流失败: {task_id}, 错误: {e}")

        except Exception as e:
            logger.error(f"清理任务资源失败: {task_id}, 错误: {e}")

    @classmethod
    async def _emergency_cleanup_task(cls, task_id: int):
        """
        紧急清理任务资源（用于数据库连接错误等严重情况）
        """
        try:
            logger.warning(f"开始紧急清理任务资源: {task_id}")

            # 立即停止监控流
            try:
                cls.stop_monitor_stream(task_id)
                logger.info(f"紧急停止监控流成功: {task_id}")
            except Exception as e:
                logger.error(f"紧急停止监控流失败: {task_id}, 错误: {e}")

            # 强制取消并移除运行任务
            try:
                if task_id in cls.running_tasks:
                    task_process = cls.running_tasks[task_id]['process']
                    if task_process and not task_process.done():
                        task_process.cancel()
                        # 等待任务取消完成
                        try:
                            await asyncio.wait_for(task_process, timeout=2.0)
                        except (asyncio.CancelledError, asyncio.TimeoutError):
                            logger.info(f"任务已强制取消: {task_id}")
                    del cls.running_tasks[task_id]
                    logger.info(f"从运行任务列表强制移除: {task_id}")
            except Exception as e:
                logger.error(f"强制移除运行任务失败: {task_id}, 错误: {e}")

            # 清理任务缓存
            try:
                cls._clear_task_cache(task_id)
                logger.info(f"清理任务缓存成功: {task_id}")
            except Exception as e:
                logger.error(f"清理任务缓存失败: {task_id}, 错误: {e}")

            # 清理车辆跟踪缓存
            try:
                if task_id in cls.vehicle_tracking_cache:
                    del cls.vehicle_tracking_cache[task_id]
                    logger.info(f"清理车辆跟踪缓存成功: {task_id}")
            except Exception as e:
                logger.error(f"清理车辆跟踪缓存失败: {task_id}, 错误: {e}")

            logger.warning(f"紧急清理任务资源完成: {task_id}")

        except Exception as e:
            logger.error(f"紧急清理任务资源失败: {task_id}, 错误: {e}")

    @classmethod
    async def _force_update_task_status_to_stopped(cls, task_id: int):
        """
        强制更新任务状态为停止（使用新的数据库连接）
        """
        try:
            from config.get_db import get_db_session
            from sqlalchemy import update

            logger.warning(f"强制更新任务状态为停止: {task_id}")

            # 创建新的数据库会话
            async with get_db_session() as new_db:
                stmt = (
                    update(SurveillanceTask)
                    .where(SurveillanceTask.task_id == task_id)
                    .values(
                        status='0',
                        update_time=datetime.now(),
                        remark='因数据库连接错误强制停止'
                    )
                )
                await new_db.execute(stmt)
                await new_db.commit()
                logger.info(f"强制更新任务状态成功: {task_id}")

        except Exception as e:
            logger.error(f"强制更新任务状态失败: {task_id}, 错误: {e}")

    @classmethod
    async def check_and_fix_task_status(cls, query_db: AsyncSession):
        """
        检查并修复任务状态不一致的问题
        """
        try:
            from module_stream.dao.task_dao import TaskDao

            # 获取所有状态为运行中的任务
            running_tasks_in_db = await TaskDao.get_running_tasks(query_db)

            for task in running_tasks_in_db:
                task_id = task.task_id

                # 检查是否真的在运行
                is_actually_running = (
                    task_id in cls.running_tasks and
                    cls.running_tasks[task_id]['process'] and
                    not cls.running_tasks[task_id]['process'].done()
                )

                if not is_actually_running:
                    logger.warning(f"发现状态不一致的任务: {task_id}, 数据库显示运行中但实际未运行")
                    # 修复状态
                    await cls._cleanup_task_resources(task_id)
                    await cls._update_task_status(query_db, task_id, '0')
                    logger.info(f"已修复任务状态: {task_id}")

            # 清理运行列表中已完成的任务
            completed_tasks = []
            for task_id, task_info in cls.running_tasks.items():
                if task_info['process'].done():
                    completed_tasks.append(task_id)

            for task_id in completed_tasks:
                logger.info(f"清理已完成的任务: {task_id}")
                await cls._cleanup_task_resources(task_id)
                await cls._update_task_status(query_db, task_id, '0')

        except Exception as e:
            logger.error(f"检查和修复任务状态失败: {e}")

    @classmethod
    async def stop_all_tasks(cls, query_db: AsyncSession):
        """
        停止所有运行中的任务
        """
        try:
            for task_id in list(cls.running_tasks.keys()):
                try:
                    await cls._cleanup_task_resources(task_id)
                    # 更新数据库状态
                    await cls._update_task_status(query_db, task_id, '0')
                except Exception as e:
                    logger.error(f"停止任务 {task_id} 失败: {e}")

            cls.running_tasks.clear()
            logger.info("所有任务已停止")

        except Exception as e:
            logger.error(f"停止所有任务失败: {e}")

    # ==================== 实时监控流相关方法 ====================

    @classmethod
    def _push_monitor_frame(
        cls,
        task_id: int,
        frame,
        detection_result: Dict[str, Any] = None,
        alert_result: Dict[str, Any] = None
    ):
        """
        推送实时监控帧 - 高性能版本，参考yolo_ROI_ai.py的优化设计
        """
        try:
            import cv2

            if task_id not in cls.monitor_streams:
                return

            if frame is None:
                return

            # ==================== 性能优化1：帧复制优化 ====================
            # 参考yolo_ROI_ai.py，每次都从原始帧的全新副本开始绘制
            # 确保前一帧的检测框不会残留到当前帧
            # 使用copy()而不是deepcopy()提高性能，对于numpy数组足够了
            monitor_frame = frame.copy()

            # ==================== 性能优化2：区域判断优化 ====================
            # 只绘制在配置区域内的检测框，减少无关绘制
            frame_height, frame_width = monitor_frame.shape[:2]

            # 检查是否有检测结果和告警
            has_detection = bool(detection_result and len(detection_result) > 0)
            has_alert = bool(alert_result and alert_result.get('hit'))

            # 添加调试日志
            logger.debug(f"推送监控帧: task_id={task_id}, 检测={has_detection}, 告警={has_alert}, 帧尺寸={frame_width}x{frame_height}")

            # ==================== 绘制逻辑优化 ====================
            if alert_result:
                # 有算法包处理结果（无论是否告警都使用算法包的结果）
                # 算法包已经处理了区域过滤、颜色标记等逻辑
                cls._draw_detection_boxes_optimized(monitor_frame, alert_result, frame_width, frame_height)

                # 统计信息
                details = alert_result.get('details', {}) if isinstance(alert_result, dict) else {}
                detections = details.get('detections', [])
                configured_areas = details.get('configured_areas', [])

                if alert_result.get('hit'):
                    logger.debug(f"告警推送: 任务{task_id}, 告警消息: {alert_result.get('message', 'N/A')}")
                else:
                    logger.debug(f"正常推送: 任务{task_id}, 算法包处理后{len(detections)}个检测框, {len(configured_areas)}个区域")

            elif detection_result:
                # 降级处理：只有原始检测结果，没有算法包处理结果
                # 这种情况通常不应该发生，因为算法包总是会被调用
                logger.warning(f"任务{task_id}只有原始检测结果，缺少算法包处理结果，使用降级显示")
                try:
                    # 获取任务配置（包含区域信息）
                    cached_config = cls.task_config_cache.get(task_id)
                    if not cached_config:
                        logger.warning(f"任务{task_id}配置缓存不存在，跳过区域过滤")
                        configured_areas = []
                        configured_lines = []
                        filtered_detections = detection_result  # 不过滤，显示所有检测
                    else:
                        # 从缓存配置中提取区域和线段信息
                        algorithm_config = cached_config.get('algorithm_config', {})
                        bbox_config = cached_config.get('bbox_config', {})

                        # 提取配置的区域
                        configured_areas = []
                        detection_areas = algorithm_config.get('detection_areas', [])
                        polygons_config = bbox_config.get('polygons', [])

                        # 合并区域配置
                        for area in detection_areas:
                            configured_areas.append({
                                'name': area.get('name', '区域'),
                                'points': area.get('points', [])
                            })

                        for polygon in polygons_config:
                            configured_areas.append({
                                'name': polygon.get('name', '多边形'),
                                'points': polygon.get('points', [])
                            })

                        # 提取配置的线段
                        configured_lines = []
                        detection_lines = algorithm_config.get('detection_lines', [])
                        lines_config = bbox_config.get('lines', [])

                        # 合并线段配置
                        for line in detection_lines:
                            configured_lines.append({
                                'name': line.get('name', '线段'),
                                'points': line.get('points', [])
                            })

                        for line in lines_config:
                            configured_lines.append({
                                'name': line.get('name', '线段'),
                                'start_point': line.get('start_point', {}),
                                'end_point': line.get('end_point', {})
                            })

                        # 如果有配置区域，则只显示区域内的检测框
                        if configured_areas:
                            filtered_detections = cls._filter_detections_by_areas(
                                detection_result, configured_areas, (frame_height, frame_width)
                            )
                            logger.debug(f"降级区域过滤: 原始{len(detection_result)}个 -> 过滤后{len(filtered_detections)}个")
                        else:
                            # 没有配置区域，显示所有检测（但这通常不应该发生）
                            filtered_detections = detection_result
                            logger.warning(f"任务{task_id}没有配置检测区域，显示所有检测框")

                    # 构造临时alert_result，包含正确的配置信息和过滤后的检测结果
                    temp_alert_result = {
                        'hit': False,
                        'message': f'检测到 {len(filtered_detections)} 个目标（降级显示）',
                        'details': {
                            'detections': filtered_detections,  # 使用过滤后的检测结果
                            'configured_areas': configured_areas,  # 使用真实的配置区域
                            'configured_lines': configured_lines   # 使用真实的配置线段
                        }
                    }
                    cls._draw_detection_boxes_optimized(monitor_frame, temp_alert_result, frame_width, frame_height)

                except Exception as e:
                    logger.error(f"降级处理检测结果失败: {e}")
                    # 最终降级：显示所有检测框但不显示区域
                    temp_alert_result = {
                        'hit': False,
                        'message': f'检测到 {len(detection_result)} 个目标（最终降级）',
                        'details': {
                            'detections': detection_result,
                            'configured_areas': [],
                            'configured_lines': []
                        }
                    }
                    cls._draw_detection_boxes_optimized(monitor_frame, temp_alert_result, frame_width, frame_height)
            else:
                # 无检测结果，只绘制配置区域
                try:
                    # 获取任务配置（包含区域信息）
                    cached_config = cls.task_config_cache.get(task_id)
                    if not cached_config:
                        logger.warning(f"任务{task_id}配置缓存不存在，无法显示配置区域")
                        configured_areas = []
                        configured_lines = []
                    else:
                        # 从缓存配置中提取区域和线段信息
                        algorithm_config = cached_config.get('algorithm_config', {})
                        bbox_config = cached_config.get('bbox_config', {})

                        # 提取配置的区域
                        configured_areas = []
                        detection_areas = algorithm_config.get('detection_areas', [])
                        polygons_config = bbox_config.get('polygons', [])

                        # 合并区域配置
                        for area in detection_areas:
                            configured_areas.append({
                                'name': area.get('name', '区域'),
                                'points': area.get('points', [])
                            })

                        for polygon in polygons_config:
                            configured_areas.append({
                                'name': polygon.get('name', '多边形'),
                                'points': polygon.get('points', [])
                            })

                        # 提取配置的线段
                        configured_lines = []
                        detection_lines = algorithm_config.get('detection_lines', [])
                        lines_config = bbox_config.get('lines', [])

                        # 合并线段配置
                        for line in detection_lines:
                            configured_lines.append({
                                'name': line.get('name', '线段'),
                                'points': line.get('points', [])
                            })

                        for line in lines_config:
                            configured_lines.append({
                                'name': line.get('name', '线段'),
                                'start_point': line.get('start_point', {}),
                                'end_point': line.get('end_point', {})
                            })

                    # 构造空的alert_result，但包含正确的配置信息
                    empty_alert_result = {
                        'hit': False,
                        'message': '监控中',
                        'details': {
                            'detections': [],
                            'configured_areas': configured_areas,  # 使用真实的配置区域
                            'configured_lines': configured_lines   # 使用真实的配置线段
                        }
                    }
                    cls._draw_detection_boxes_optimized(monitor_frame, empty_alert_result, frame_width, frame_height)

                except Exception as e:
                    logger.error(f"处理无检测结果失败: {e}")
                    # 降级处理：不显示任何区域
                    empty_alert_result = {
                        'hit': False,
                        'message': '监控中',
                        'details': {
                            'detections': [],
                            'configured_areas': [],
                            'configured_lines': []
                        }
                    }
                    cls._draw_detection_boxes_optimized(monitor_frame, empty_alert_result, frame_width, frame_height)

            # 优化编码参数：降低质量但提高速度，类似yolo_ROI_ai.py
            encode_params = [cv2.IMWRITE_JPEG_QUALITY, 60]  # 降低质量提高速度
            success, buffer = cv2.imencode('.jpg', monitor_frame, encode_params)

            if not success:
                logger.error(f"任务 {task_id} 帧编码失败")
                return

            frame_data = buffer.tobytes()

            # 检查detection_result和alert_result的格式
            has_detection = False
            if detection_result:
                if isinstance(detection_result, list):
                    has_detection = len(detection_result) > 0
                elif isinstance(detection_result, dict):
                    has_detection = bool(detection_result.get('boxes') or detection_result.get('detections'))

            has_alert = False
            if alert_result:
                if isinstance(alert_result, dict):
                    has_alert = bool(alert_result.get('hit'))

            # 推送到队列（保持实时性，类似yolo_ROI_ai.py的队列管理）
            frame_queue = cls.monitor_streams[task_id]['frame_queue']
            frame_info = {
                'frame_data': frame_data,
                'timestamp': time.time(),
                'has_detection': has_detection,
                'has_alert': has_alert,
                'frame_size': len(frame_data)
            }

            # 如果有告警，添加告警详细信息
            if has_alert and alert_result:
                frame_info['alert_message'] = alert_result.get('message', '检测到异常')
                frame_info['alert_level'] = alert_result.get('level', '2')  # 默认中级告警
                frame_info['alert_type'] = alert_result.get('type', 'unknown')
                frame_info['alert_details'] = alert_result.get('details', {})

            try:
                # 非阻塞推送，如果队列满了就丢弃旧帧（保持实时性）
                frame_queue.put_nowait(frame_info)
            except queue.Full:
                # 队列满了，移除最旧的帧再推送新帧
                try:
                    _ = frame_queue.get_nowait()  # 丢弃旧帧
                    frame_queue.put_nowait(frame_info)
                    logger.debug(f"任务 {task_id} 队列满，替换旧帧")
                except queue.Empty:
                    # 队列为空，直接推送
                    try:
                        frame_queue.put_nowait(frame_info)
                    except queue.Full:
                        logger.warning(f"任务 {task_id} 队列操作异常")

        except Exception as e:
            logger.error(f"任务 {task_id} 推送监控帧失败: {e}")
            import traceback
            traceback.print_exc()

    @classmethod
    def _draw_detection_boxes_optimized(
        cls,
        frame,
        alert_result: Dict[str, Any],
        frame_width: int,
        frame_height: int
    ):
        """
        优化的检测框绘制方法，参考yolo_ROI_ai.py的高效绘制

        主要优化：
        1. 只绘制在配置区域内的检测框
        2. 减少重复的区域判断计算
        3. 优化绘制顺序和方法
        4. 确保不修改原始帧
        5. 每次绘制都从干净的帧开始
        """
        try:
            logger.debug(f"开始优化绘制: 帧尺寸={frame_width}x{frame_height}")

            details = alert_result.get('details', {}) if alert_result else {}

            # 始终绘制配置的区域和线段（作为监控范围显示）
            # 确保区域始终显示，不会闪烁
            # 优化：使用稳定的区域绘制逻辑
            cls._draw_configured_areas_and_lines_stable(frame, alert_result or {})

            # 获取检测结果（算法包已过滤区域内目标）
            detections = details.get('detections', [])

            # 过滤可能的历史目标（算法包跟踪器可能返回已离开的目标）
            # 通过检查检测框的有效性来过滤
            valid_detections = []
            for detection in detections:
                xyxy = detection.get('xyxy', detection.get('bbox', detection.get('box', [])))
                if len(xyxy) >= 4:
                    x1, y1, x2, y2 = xyxy
                    # 检查检测框是否有效（面积大于0，坐标合理）
                    if x2 > x1 and y2 > y1 and x1 >= 0 and y1 >= 0:
                        valid_detections.append(detection)
                    else:
                        logger.debug(f"过滤无效检测框: track_id={detection.get('track_id')}, xyxy={xyxy}")

            detections = valid_detections
            logger.debug(f"检测结果数量: {len(detections)}（已过滤无效目标）")

            if not detections:
                logger.debug("无检测结果，跳过检测框绘制")
                return

            # 绘制检测框（算法包已过滤，直接绘制）
            drawn_count = 0
            skipped_count = 0

            for detection in detections:
                xyxy = detection.get('xyxy', detection.get('bbox', detection.get('box', [])))
                if len(xyxy) < 4:
                    skipped_count += 1
                    continue

                provided_color = detection.get('color', None)
                track_id = detection.get('track_id', 'N/A')
                logger.debug(f"处理检测目标: track_id={track_id}, color={provided_color}, xyxy={xyxy}")

                # 检查单个检测对象是否有告警原因（由算法包指定）
                has_alert_reason = bool(detection.get('alert_reason'))

                if has_alert_reason:
                    # 有告警原因：绘制告警样式的检测框（红色或算法包指定颜色）
                    cls._draw_single_detection_as_alert(frame, detection, provided_color)
                else:
                    # 无告警原因：绘制普通样式的检测框（绿色或算法包指定颜色）
                    cls._draw_single_detection_as_normal(frame, detection, provided_color)

                drawn_count += 1

            logger.debug(f"绘制完成: 绘制{drawn_count}个检测框, 跳过{skipped_count}个")

        except Exception as e:
            logger.error(f"优化绘制检测框失败: {e}")

    @classmethod
    def _filter_detections_by_areas(
        cls,
        detections: List[Dict],
        configured_areas: List[Dict],
        frame_shape: tuple
    ) -> List[Dict]:
        """
        根据配置区域过滤检测结果，只返回在区域内的检测框

        :param detections: 原始检测结果列表
        :param configured_areas: 配置的检测区域列表
        :param frame_shape: 帧尺寸 (height, width)
        :return: 过滤后的检测结果列表
        """
        if not configured_areas or not detections:
            return detections

        try:
            height, width = frame_shape[:2]
            filtered_detections = []

            for detection in detections:
                # 获取检测框坐标
                xyxy = detection.get('xyxy', detection.get('bbox', detection.get('box', [])))
                if len(xyxy) < 4:
                    continue

                # 计算检测框中心点
                center_x = (xyxy[0] + xyxy[2]) / 2
                center_y = (xyxy[1] + xyxy[3]) / 2

                # 检查是否在任何配置区域内
                is_in_area = False
                for area in configured_areas:
                    points = area.get('points', [])
                    if len(points) >= 3:
                        # 转换点坐标格式
                        polygon_points = []
                        for point in points:
                            if isinstance(point, dict) and 'x' in point and 'y' in point:
                                # 处理相对坐标
                                if 0 <= point['x'] <= 1 and 0 <= point['y'] <= 1:
                                    x = int(point['x'] * width)
                                    y = int(point['y'] * height)
                                else:
                                    x = int(point['x'])
                                    y = int(point['y'])
                                polygon_points.append([x, y])
                            elif isinstance(point, (list, tuple)) and len(point) >= 2:
                                polygon_points.append([int(point[0]), int(point[1])])

                        # 使用点在多边形内判断
                        if len(polygon_points) >= 3 and cls._point_in_polygon_simple([center_x, center_y], polygon_points):
                            is_in_area = True
                            break

                # 只保留在区域内的检测框
                if is_in_area:
                    filtered_detections.append(detection)

            logger.debug(f"区域过滤完成: 原始{len(detections)}个检测 -> 区域内{len(filtered_detections)}个检测")
            return filtered_detections

        except Exception as e:
            logger.error(f"区域过滤失败: {e}")
            # 过滤失败时返回原始检测结果
            return detections

    @classmethod
    def _point_in_polygon_simple(cls, point, polygon):
        """
        判断点是否在多边形内（射线法）- 简化版本

        :param point: 点坐标 [x, y]
        :param polygon: 多边形顶点列表 [[x1, y1], [x2, y2], ...]
        :return: 是否在多边形内
        """
        try:
            x, y = point
            n = len(polygon)
            inside = False
            p1x, p1y = polygon[0]

            for i in range(1, n + 1):
                p2x, p2y = polygon[i % n]
                if y > min(p1y, p2y):
                    if y <= max(p1y, p2y):
                        if x <= max(p1x, p2x):
                            if p1y != p2y:
                                xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                            if p1x == p2x or x <= xinters:
                                inside = not inside
                p1x, p1y = p2x, p2y

            return inside

        except Exception as e:
            logger.error(f"点在多边形内判断失败: {e}")
            return False

    @classmethod
    def _is_detection_in_areas_fast(cls, xyxy: List[int], processed_areas: List[List[tuple]]) -> bool:
        """
        快速区域判断，使用预处理的区域坐标
        """
        try:
            if not processed_areas:
                return True

            # 计算检测框中心点
            x1, y1, x2, y2 = xyxy
            center_x = (x1 + x2) // 2
            center_y = (y1 + y2) // 2

            # 检查是否在任何区域内
            for polygon_points in processed_areas:
                if cls._point_in_polygon((center_x, center_y), polygon_points):
                    return True

            return False

        except Exception as e:
            logger.error(f"快速区域判断失败: {e}")
            return True  # 出错时默认绘制

    @classmethod
    def start_monitor_stream(cls, task_id: int) -> bool:
        """
        启动任务的实时监控流
        """
        try:
            if task_id in cls.monitor_streams:
                logger.info(f"任务 {task_id} 的监控流已存在")
                return True

            # 创建帧队列和客户端集合 - 优化：减少队列大小降低延迟
            cls.monitor_streams[task_id] = {
                'frame_queue': queue.Queue(maxsize=3),  # 减少到3帧，降低延迟
                'clients': set(),
                'created_at': time.time()
            }

            logger.info(f"任务 {task_id} 的监控流已启动")
            return True

        except Exception as e:
            logger.error(f"启动监控流失败: {e}")
            return False

    @classmethod
    def stop_monitor_stream(cls, task_id: int):
        """
        停止任务的实时监控流
        """
        try:
            if task_id in cls.monitor_streams:
                # 清空队列
                frame_queue = cls.monitor_streams[task_id]['frame_queue']
                while not frame_queue.empty():
                    try:
                        frame_queue.get_nowait()
                    except queue.Empty:
                        break

                # 移除监控流
                del cls.monitor_streams[task_id]
                logger.info(f"任务 {task_id} 的监控流已停止")

        except Exception as e:
            logger.error(f"停止监控流失败: {e}")

    @classmethod
    def get_monitor_frame(cls, task_id: int, timeout: float = 1.0) -> Optional[Dict]:
        """
        获取监控帧（用于WebSocket推送）
        """
        try:
            if task_id not in cls.monitor_streams:
                return None

            frame_queue = cls.monitor_streams[task_id]['frame_queue']
            try:
                frame_data = frame_queue.get(timeout=timeout)
                return frame_data
            except queue.Empty:
                return None

        except Exception as e:
            logger.error(f"获取监控帧失败: {e}")
            return None

    @classmethod
    def get_latest_monitor_frame(cls, task_id: int) -> Optional[Dict]:
        """
        获取最新的监控帧（非阻塞，用于保持画面更新）
        """
        try:
            if task_id not in cls.monitor_streams:
                return None

            frame_queue = cls.monitor_streams[task_id]['frame_queue']
            latest_frame = None

            # 获取队列中所有帧，保留最新的一帧
            while True:
                try:
                    frame_data = frame_queue.get_nowait()
                    latest_frame = frame_data
                except queue.Empty:
                    break

            return latest_frame

        except Exception as e:
            logger.error(f"获取最新监控帧失败: {e}")
            return None

    @classmethod
    def add_monitor_client(cls, task_id: int, client_id: str):
        """
        添加监控客户端
        """
        try:
            if task_id in cls.monitor_streams:
                cls.monitor_streams[task_id]['clients'].add(client_id)
                logger.info(f"客户端 {client_id} 已连接到任务 {task_id} 的监控流")

        except Exception as e:
            logger.error(f"添加监控客户端失败: {e}")

    @classmethod
    def remove_monitor_client(cls, task_id: int, client_id: str):
        """
        移除监控客户端
        """
        try:
            if task_id in cls.monitor_streams:
                cls.monitor_streams[task_id]['clients'].discard(client_id)
                logger.info(f"客户端 {client_id} 已断开任务 {task_id} 的监控流")

                # 如果没有客户端了，考虑停止监控流
                if not cls.monitor_streams[task_id]['clients']:
                    logger.info(f"任务 {task_id} 没有监控客户端，保持监控流运行")

        except Exception as e:
            logger.error(f"移除监控客户端失败: {e}")

    @classmethod
    def get_monitor_stats(cls, task_id: int) -> Dict[str, Any]:
        """
        获取监控流统计信息
        """
        try:
            if task_id not in cls.monitor_streams:
                return {'status': 'not_found'}

            monitor_info = cls.monitor_streams[task_id]
            return {
                'status': 'active',
                'client_count': len(monitor_info['clients']),
                'queue_size': monitor_info['frame_queue'].qsize(),
                'created_at': monitor_info['created_at'],
                'uptime': time.time() - monitor_info['created_at']
            }

        except Exception as e:
            logger.error(f"获取监控统计失败: {e}")
            return {'status': 'error', 'message': str(e)}
