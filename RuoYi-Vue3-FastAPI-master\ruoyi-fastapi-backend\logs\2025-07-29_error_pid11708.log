2025-07-29 09:21:50.080 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-29 09:21:50.083 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-29 09:21:50.128 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-29 09:21:50.129 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-29 09:21:50.130 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-29 09:21:50.235 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-29 09:21:50.258 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-29 09:21:50.258 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-29 09:21:59.642 | 66995aabd814479b913d5952609d7419 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-29 09:22:03.689 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 961: 实体对象 = True
2025-07-29 09:22:03.689 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091603_648.jpg
2025-07-29 09:22:03.690 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091603_648.jpg
2025-07-29 09:22:03.691 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 961
2025-07-29 09:22:03.692 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 960: 实体对象 = True
2025-07-29 09:22:03.693 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091602_395.jpg
2025-07-29 09:22:03.693 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091602_395.jpg
2025-07-29 09:22:03.695 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 960
2025-07-29 09:22:03.696 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 959: 实体对象 = True
2025-07-29 09:22:03.697 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091601_120.jpg
2025-07-29 09:22:03.697 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091601_120.jpg
2025-07-29 09:22:03.698 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 959
2025-07-29 09:22:03.700 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 958: 实体对象 = True
2025-07-29 09:22:03.701 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091559_860.jpg
2025-07-29 09:22:03.701 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091559_860.jpg
2025-07-29 09:22:03.702 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 958
2025-07-29 09:22:03.703 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 957: 实体对象 = True
2025-07-29 09:22:03.704 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091558_623.jpg
2025-07-29 09:22:03.704 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091558_623.jpg
2025-07-29 09:22:03.705 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 957
2025-07-29 09:22:03.707 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 956: 实体对象 = True
2025-07-29 09:22:03.707 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091557_422.jpg
2025-07-29 09:22:03.708 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091557_422.jpg
2025-07-29 09:22:03.709 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 956
2025-07-29 09:22:03.710 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 955: 实体对象 = True
2025-07-29 09:22:03.710 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091556_282.jpg
2025-07-29 09:22:03.711 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091556_282.jpg
2025-07-29 09:22:03.712 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 955
2025-07-29 09:22:03.714 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 954: 实体对象 = True
2025-07-29 09:22:03.714 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091555_115.jpg
2025-07-29 09:22:03.715 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091555_115.jpg
2025-07-29 09:22:03.716 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 954
2025-07-29 09:22:03.717 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 953: 实体对象 = True
2025-07-29 09:22:03.717 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091554_225.jpg
2025-07-29 09:22:03.717 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091554_225.jpg
2025-07-29 09:22:03.720 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 953
2025-07-29 09:22:03.721 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 952: 实体对象 = True
2025-07-29 09:22:03.722 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091553_369.jpg
2025-07-29 09:22:03.722 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091553_369.jpg
2025-07-29 09:22:03.723 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 952
2025-07-29 09:22:03.724 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 951: 实体对象 = True
2025-07-29 09:22:03.725 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091552_225.jpg
2025-07-29 09:22:03.725 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091552_225.jpg
2025-07-29 09:22:03.726 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 951
2025-07-29 09:22:03.727 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 950: 实体对象 = True
2025-07-29 09:22:03.728 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091551_609.jpg
2025-07-29 09:22:03.728 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091551_609.jpg
2025-07-29 09:22:03.729 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 950
2025-07-29 09:22:03.730 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 949: 实体对象 = True
2025-07-29 09:22:03.731 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091551_014.jpg
2025-07-29 09:22:03.731 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091551_014.jpg
2025-07-29 09:22:03.733 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 949
2025-07-29 09:22:03.734 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 948: 实体对象 = True
2025-07-29 09:22:03.734 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091550_439.jpg
2025-07-29 09:22:03.735 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091550_439.jpg
2025-07-29 09:22:03.736 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 948
2025-07-29 09:22:03.738 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 947: 实体对象 = True
2025-07-29 09:22:03.739 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091549_879.jpg
2025-07-29 09:22:03.739 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091549_879.jpg
2025-07-29 09:22:03.740 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 947
2025-07-29 09:22:03.741 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 946: 实体对象 = True
2025-07-29 09:22:03.742 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091549_333.jpg
2025-07-29 09:22:03.742 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091549_333.jpg
2025-07-29 09:22:03.743 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 946
2025-07-29 09:22:03.744 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 945: 实体对象 = True
2025-07-29 09:22:03.745 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091548_731.jpg
2025-07-29 09:22:03.745 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091548_731.jpg
2025-07-29 09:22:03.746 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 945
2025-07-29 09:22:03.747 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 944: 实体对象 = True
2025-07-29 09:22:03.748 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091548_183.jpg
2025-07-29 09:22:03.748 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091548_183.jpg
2025-07-29 09:22:03.749 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 944
2025-07-29 09:22:03.751 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 943: 实体对象 = True
2025-07-29 09:22:03.751 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091547_653.jpg
2025-07-29 09:22:03.751 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091547_653.jpg
2025-07-29 09:22:03.752 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 943
2025-07-29 09:22:03.753 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 942: 实体对象 = True
2025-07-29 09:22:03.754 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091547_138.jpg
2025-07-29 09:22:03.754 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091547_138.jpg
2025-07-29 09:22:03.755 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 942
2025-07-29 09:22:03.757 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 941: 实体对象 = True
2025-07-29 09:22:03.757 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091546_639.jpg
2025-07-29 09:22:03.757 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091546_639.jpg
2025-07-29 09:22:03.758 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 941
2025-07-29 09:22:03.759 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 940: 实体对象 = True
2025-07-29 09:22:03.760 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091546_312.jpg
2025-07-29 09:22:03.760 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091546_312.jpg
2025-07-29 09:22:03.775 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 940
2025-07-29 09:22:03.777 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 939: 实体对象 = True
2025-07-29 09:22:03.777 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_13_8_20250729_091545_999.jpg
2025-07-29 09:22:03.778 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_13_8_20250729_091545_999.jpg
2025-07-29 09:22:03.783 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 939
2025-07-29 09:22:03.788 | e5685fc903df4f8799bf9739c8871f8d | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除23条记录，删除23个截图文件
2025-07-29 09:22:03.824 | 9c825e9acb6c45eb9456ae1303c6a277 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-29 09:22:06.150 | f85fb378e0aa47e0a2797bb10c598d38 | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-29 09:22:06.165 | 1a8c607000c34dc4a846ecbbe7c13899 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-29 09:22:06.339 | 4c667e9dd3794edcaa958e530f9f6288 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-29 09:22:08.659 | 46fb518a593e4e5c9224750af050011e | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 09:22:08.659 | 46fb518a593e4e5c9224750af050011e | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 09:22:08.659 | 46fb518a593e4e5c9224750af050011e | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-29 09:22:08.659 | 46fb518a593e4e5c9224750af050011e | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 09:22:08.659 | 46fb518a593e4e5c9224750af050011e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 09:22:08.660 | 46fb518a593e4e5c9224750af050011e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 09:22:08.660 | 46fb518a593e4e5c9224750af050011e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-29 09:22:08.660 | 46fb518a593e4e5c9224750af050011e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 09:22:08.660 | 46fb518a593e4e5c9224750af050011e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 09:22:08.665 | 46fb518a593e4e5c9224750af050011e | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 09:22:08.665 | 46fb518a593e4e5c9224750af050011e | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 09:22:08.665 | 46fb518a593e4e5c9224750af050011e | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 09:22:08.666 | 46fb518a593e4e5c9224750af050011e | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 09:22:08.668 | 46fb518a593e4e5c9224750af050011e | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆监控
2025-07-29 09:22:08.668 | 46fb518a593e4e5c9224750af050011e | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 09:22:08.668 | 46fb518a593e4e5c9224750af050011e | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 09:22:08.668 | 46fb518a593e4e5c9224750af050011e | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-29 09:22:10.334 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-29 09:22:10.334 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 09:22:10.335 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 09:22:10.335 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:22:10.335 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 09:22:10.335 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 09:22:10.336 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 09:22:10.336 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:22:10.336 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 09:22:10.337 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 09:22:10.340 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 09:22:10.340 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 09:22:10.340 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 09:22:10.340 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 09:22:10.341 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆监控
2025-07-29 09:22:10.341 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 09:22:10.341 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 09:22:10.342 | d6f5fa5b401c4edea6bc2bb811481122 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-29 09:22:12.323 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:start_task:204 - 开始启动任务: 13
2025-07-29 09:22:12.327 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:498 - 加载算法配置: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info']
2025-07-29 09:22:12.327 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:505 - 加载检测区域配置: ['version', 'created_at', 'detection_areas', 'detection_lines', 'exclusion_areas']
2025-07-29 09:22:12.327 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:512 - 加载告警配置: ['version', 'created_at', 'alert_params']
2025-07-29 09:22:12.327 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:519 - 加载用户配置（优先级最高）: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-29 09:22:12.327 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:525 - 成功加载数据库配置，包含字段: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_params', 'algorithm_id', 'algorithm_name', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-29 09:22:12.327 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:526 - 配置加载优先级: algorithm_config < bbox_config < alert_config < user_config
2025-07-29 09:22:12.328 | 08d8b727aa5640fe99dfc689d0bc64bc | WARNING  | module_stream.service.task_execution_service:_validate_required_config:640 - 算法 car_counting 缺少配置参数: 置信度阈值 (confidence_threshold 或 conf_thres), NMS阈值 (nms_threshold 或 nms_thres), 输入图像尺寸 (input_size 或 img_size)。将使用默认值，建议在算法配置页面设置这些参数以获得更好的检测效果。
2025-07-29 09:22:12.328 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:664 - 验证模型初始化 - 添加YOLOv5路径: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master
2025-07-29 09:22:12.329 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:679 - 验证模型初始化 - 成功预导入YOLOv5 utils模块
2025-07-29 09:22:12.329 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:684 - 验证模型初始化 - 当前sys.path前5项: ['D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master\\utils', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master', 'D:\\ai-recognition\\RuoYi-Vue3-FastAPI-master\\ruoyi-fastapi-backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs']
2025-07-29 09:22:12.329 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:685 - 验证模型初始化 - 当前工作目录: D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend
2025-07-29 09:22:12.329 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:689 - 验证模型初始化 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-29 09:22:13.659 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:710 - 验证模型初始化 - 成功导入智驱力模型
2025-07-29 09:22:13.660 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.13
2025-07-29 09:22:13.660 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-29 09:22:13.660 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-29 09:22:13.660 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.13, 'nms_thres': 0.5}
2025-07-29 09:22:13.660 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:722 - 验证模型初始化 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.13, 'nms_thres': 0.5}
2025-07-29 09:22:16.020 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:734 - 验证模型初始化 - 智驱力模型初始化成功
2025-07-29 09:22:16.020 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:735 -    - 设备: cuda
2025-07-29 09:22:16.020 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:736 -    - 图像尺寸: 640
2025-07-29 09:22:16.021 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:737 -    - 置信度阈值: 0.13
2025-07-29 09:22:16.021 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:738 -    - NMS阈值: 0.5
2025-07-29 09:22:16.023 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:751 - 验证模型初始化 - 智驱力后处理器初始化成功
2025-07-29 09:22:16.037 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:start_monitor_stream:4104 - 任务 13 的监控流已启动
2025-07-29 09:22:16.040 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_cache_task_config:112 - 任务13配置已缓存
2025-07-29 09:22:16.040 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:detection_loop:799 - 任务13配置缓存完成: 区域1个, 线段0个
2025-07-29 09:22:16.041 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:detection_loop:829 - 成功预导入YOLOv5 utils模块
2025-07-29 09:22:16.041 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:detection_loop:835 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-29 09:22:16.042 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: zql_detect
2025-07-29 09:22:16.042 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: model
2025-07-29 09:22:16.042 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:detection_loop:855 - 成功导入智驱力模型
2025-07-29 09:22:16.043 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.13
2025-07-29 09:22:16.043 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-29 09:22:16.043 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-29 09:22:16.043 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.13, 'nms_thres': 0.5}
2025-07-29 09:22:16.043 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:detection_loop:867 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.13, 'nms_thres': 0.5}
2025-07-29 09:22:16.170 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:detection_loop:880 - 智驱力模型初始化成功
2025-07-29 09:22:16.170 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:detection_loop:881 -    - 设备: cuda
2025-07-29 09:22:16.170 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:detection_loop:882 -    - 图像尺寸: 640
2025-07-29 09:22:16.170 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:detection_loop:883 -    - 置信度阈值: 0.13
2025-07-29 09:22:16.170 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:detection_loop:884 -    - NMS阈值: 0.5
2025-07-29 09:22:16.171 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:detection_loop:897 - 智驱力后处理器初始化成功
2025-07-29 09:22:46.192 | 08d8b727aa5640fe99dfc689d0bc64bc | ERROR    | module_stream.service.task_execution_service:detection_loop:917 - 视频流连接超时或失败: rtsp://127.0.0.1:8554/test1
2025-07-29 09:22:46.195 | 08d8b727aa5640fe99dfc689d0bc64bc | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3297 - 更新任务状态失败: readexactly() called while another coroutine is already waiting for incoming data
2025-07-29 09:22:46.195 | 08d8b727aa5640fe99dfc689d0bc64bc | ERROR    | module_stream.service.task_execution_service:detection_loop:1280 - 检测过程中发生错误: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-07-29 09:22:46.195 | 08d8b727aa5640fe99dfc689d0bc64bc | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3297 - 更新任务状态失败: This session is in 'prepared' state; no further SQL can be emitted within this transaction.
2025-07-29 09:22:46.199 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.service.task_execution_service:start_task:255 - 任务 13 启动成功，包括实时监控流
2025-07-29 09:22:46.199 | 08d8b727aa5640fe99dfc689d0bc64bc | INFO     | module_stream.controller.monitor_controller:batch_start_tasks:157 - 批量启动任务完全成功: [13]
2025-07-29 09:22:46.213 | a0f285aa27414814899d042978b8d6cf | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-29 09:22:46.216 | be7d818713ee49cc855cf1e78addf1b6 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 09:22:46.216 | be7d818713ee49cc855cf1e78addf1b6 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 09:22:46.216 | be7d818713ee49cc855cf1e78addf1b6 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-29 09:22:46.216 | be7d818713ee49cc855cf1e78addf1b6 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 09:22:46.217 | be7d818713ee49cc855cf1e78addf1b6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 09:22:46.217 | be7d818713ee49cc855cf1e78addf1b6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 09:22:46.217 | be7d818713ee49cc855cf1e78addf1b6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-29 09:22:46.217 | be7d818713ee49cc855cf1e78addf1b6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 09:22:46.217 | be7d818713ee49cc855cf1e78addf1b6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 09:22:46.222 | be7d818713ee49cc855cf1e78addf1b6 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 09:22:46.222 | be7d818713ee49cc855cf1e78addf1b6 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 09:22:46.223 | be7d818713ee49cc855cf1e78addf1b6 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 09:22:46.223 | be7d818713ee49cc855cf1e78addf1b6 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 09:22:46.224 | 4baab822f27f4eea934a0cbb10e2494e | ERROR    | exceptions.handle:exception_handler:117 - 数据库连接错误: (asyncmy.errors.OperationalError) (2014, 'Command Out of Sync')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-29 09:22:46.231 | be7d818713ee49cc855cf1e78addf1b6 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆监控
2025-07-29 09:22:46.231 | be7d818713ee49cc855cf1e78addf1b6 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 09:22:46.231 | be7d818713ee49cc855cf1e78addf1b6 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 09:22:46.232 | be7d818713ee49cc855cf1e78addf1b6 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-29 09:22:54.832 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-29 09:22:54.833 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
