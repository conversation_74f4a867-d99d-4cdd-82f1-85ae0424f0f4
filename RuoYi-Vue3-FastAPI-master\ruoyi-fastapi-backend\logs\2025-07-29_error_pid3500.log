2025-07-29 08:55:42.873 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-29 08:55:42.874 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-29 08:55:42.903 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-29 08:55:42.903 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-29 08:55:42.904 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-29 08:55:42.944 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-29 08:55:42.953 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-29 08:55:42.953 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-29 08:57:59.315 | 8b463dfdd2164e4b87af1bd38a4c7435 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-29 08:58:03.782 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1896: 实体对象 = True
2025-07-29 08:58:03.783 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084434_227.jpg
2025-07-29 08:58:03.783 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084434_227.jpg
2025-07-29 08:58:03.784 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1896
2025-07-29 08:58:03.786 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1895: 实体对象 = True
2025-07-29 08:58:03.786 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084433_209.jpg
2025-07-29 08:58:03.786 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084433_209.jpg
2025-07-29 08:58:03.788 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1895
2025-07-29 08:58:03.789 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1894: 实体对象 = True
2025-07-29 08:58:03.790 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084432_606.jpg
2025-07-29 08:58:03.790 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084432_606.jpg
2025-07-29 08:58:03.791 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1894
2025-07-29 08:58:03.792 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1893: 实体对象 = True
2025-07-29 08:58:03.792 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084431_995.jpg
2025-07-29 08:58:03.792 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084431_995.jpg
2025-07-29 08:58:03.794 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1893
2025-07-29 08:58:03.795 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1892: 实体对象 = True
2025-07-29 08:58:03.796 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084431_388.jpg
2025-07-29 08:58:03.796 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084431_388.jpg
2025-07-29 08:58:03.797 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1892
2025-07-29 08:58:03.798 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1891: 实体对象 = True
2025-07-29 08:58:03.799 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084430_775.jpg
2025-07-29 08:58:03.799 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084430_775.jpg
2025-07-29 08:58:03.800 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1891
2025-07-29 08:58:03.801 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1890: 实体对象 = True
2025-07-29 08:58:03.802 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084430_162.jpg
2025-07-29 08:58:03.802 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084430_162.jpg
2025-07-29 08:58:03.803 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1890
2025-07-29 08:58:03.804 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1889: 实体对象 = True
2025-07-29 08:58:03.805 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084429_541.jpg
2025-07-29 08:58:03.805 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084429_541.jpg
2025-07-29 08:58:03.806 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1889
2025-07-29 08:58:03.807 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1888: 实体对象 = True
2025-07-29 08:58:03.808 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084428_937.jpg
2025-07-29 08:58:03.808 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084428_937.jpg
2025-07-29 08:58:03.809 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1888
2025-07-29 08:58:03.810 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1887: 实体对象 = True
2025-07-29 08:58:03.810 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084428_340.jpg
2025-07-29 08:58:03.811 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084428_340.jpg
2025-07-29 08:58:03.812 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1887
2025-07-29 08:58:03.813 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1886: 实体对象 = True
2025-07-29 08:58:03.814 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084427_765.jpg
2025-07-29 08:58:03.814 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084427_765.jpg
2025-07-29 08:58:03.815 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1886
2025-07-29 08:58:03.816 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1885: 实体对象 = True
2025-07-29 08:58:03.816 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084427_207.jpg
2025-07-29 08:58:03.816 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084427_207.jpg
2025-07-29 08:58:03.818 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1885
2025-07-29 08:58:03.819 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1884: 实体对象 = True
2025-07-29 08:58:03.819 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084426_673.jpg
2025-07-29 08:58:03.820 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084426_673.jpg
2025-07-29 08:58:03.820 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1884
2025-07-29 08:58:03.822 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1883: 实体对象 = True
2025-07-29 08:58:03.822 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084426_131.jpg
2025-07-29 08:58:03.822 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084426_131.jpg
2025-07-29 08:58:03.823 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1883
2025-07-29 08:58:03.825 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1882: 实体对象 = True
2025-07-29 08:58:03.826 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084425_598.jpg
2025-07-29 08:58:03.826 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084425_598.jpg
2025-07-29 08:58:03.827 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1882
2025-07-29 08:58:03.828 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1881: 实体对象 = True
2025-07-29 08:58:03.828 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084425_083.jpg
2025-07-29 08:58:03.828 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084425_083.jpg
2025-07-29 08:58:03.830 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1881
2025-07-29 08:58:03.831 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1880: 实体对象 = True
2025-07-29 08:58:03.831 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084424_588.jpg
2025-07-29 08:58:03.832 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084424_588.jpg
2025-07-29 08:58:03.833 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1880
2025-07-29 08:58:03.834 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1879: 实体对象 = True
2025-07-29 08:58:03.834 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084424_264.jpg
2025-07-29 08:58:03.834 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084424_264.jpg
2025-07-29 08:58:03.836 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1879
2025-07-29 08:58:03.837 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1878: 实体对象 = True
2025-07-29 08:58:03.838 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084423_781.jpg
2025-07-29 08:58:03.838 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084423_781.jpg
2025-07-29 08:58:03.839 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1878
2025-07-29 08:58:03.840 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1877: 实体对象 = True
2025-07-29 08:58:03.840 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084423_444.jpg
2025-07-29 08:58:03.840 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084423_444.jpg
2025-07-29 08:58:03.842 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1877
2025-07-29 08:58:03.843 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1876: 实体对象 = True
2025-07-29 08:58:03.844 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084422_965.jpg
2025-07-29 08:58:03.844 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084422_965.jpg
2025-07-29 08:58:03.845 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1876
2025-07-29 08:58:03.846 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1875: 实体对象 = True
2025-07-29 08:58:03.847 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084422_629.jpg
2025-07-29 08:58:03.847 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084422_629.jpg
2025-07-29 08:58:03.848 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1875
2025-07-29 08:58:03.849 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1874: 实体对象 = True
2025-07-29 08:58:03.850 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084422_154.jpg
2025-07-29 08:58:03.850 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084422_154.jpg
2025-07-29 08:58:03.851 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1874
2025-07-29 08:58:03.852 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1873: 实体对象 = True
2025-07-29 08:58:03.852 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084421_822.jpg
2025-07-29 08:58:03.852 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084421_822.jpg
2025-07-29 08:58:03.853 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1873
2025-07-29 08:58:03.855 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1872: 实体对象 = True
2025-07-29 08:58:03.855 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084421_501.jpg
2025-07-29 08:58:03.855 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084421_501.jpg
2025-07-29 08:58:03.856 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1872
2025-07-29 08:58:03.857 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1871: 实体对象 = True
2025-07-29 08:58:03.858 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084421_185.jpg
2025-07-29 08:58:03.858 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084421_185.jpg
2025-07-29 08:58:03.859 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1871
2025-07-29 08:58:03.860 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1870: 实体对象 = True
2025-07-29 08:58:03.861 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084420_893.jpg
2025-07-29 08:58:03.861 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084420_893.jpg
2025-07-29 08:58:03.862 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1870
2025-07-29 08:58:03.863 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1869: 实体对象 = True
2025-07-29 08:58:03.863 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084420_607.jpg
2025-07-29 08:58:03.863 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084420_607.jpg
2025-07-29 08:58:03.864 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1869
2025-07-29 08:58:03.865 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1868: 实体对象 = True
2025-07-29 08:58:03.866 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084415_838.jpg
2025-07-29 08:58:03.866 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084415_838.jpg
2025-07-29 08:58:03.867 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1868
2025-07-29 08:58:03.869 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1867: 实体对象 = True
2025-07-29 08:58:03.869 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_084415_612.jpg
2025-07-29 08:58:03.869 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_084415_612.jpg
2025-07-29 08:58:03.870 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1867
2025-07-29 08:58:03.875 | 278e4f802424461997740a7e7328eef4 | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除30条记录，删除30个截图文件
2025-07-29 08:58:03.907 | c5bdaf5e81644419b94b65b56ee2a5aa | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-29 08:58:07.150 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-29 08:58:07.150 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 08:58:07.150 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 08:58:07.150 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 08:58:07.151 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 08:58:07.151 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 08:58:07.151 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 08:58:07.151 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 08:58:07.152 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 08:58:07.152 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 08:58:07.156 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 08:58:07.156 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 08:58:07.157 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 08:58:07.157 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 08:58:07.159 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 08:58:07.159 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 08:58:07.159 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 08:58:07.159 | e4c1c568fcad4f1a9d19ec6ce8cf16d7 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-29 08:58:09.078 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:start_task:204 - 开始启动任务: 12
2025-07-29 08:58:09.080 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:498 - 加载算法配置: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info']
2025-07-29 08:58:09.081 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:505 - 加载检测区域配置: ['version', 'created_at', 'detection_areas', 'detection_lines', 'exclusion_areas']
2025-07-29 08:58:09.081 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:512 - 加载告警配置: ['version', 'created_at', 'alert_params']
2025-07-29 08:58:09.081 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:519 - 加载用户配置（优先级最高）: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-29 08:58:09.081 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:525 - 成功加载数据库配置，包含字段: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_params', 'algorithm_id', 'algorithm_name', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-29 08:58:09.081 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:526 - 配置加载优先级: algorithm_config < bbox_config < alert_config < user_config
2025-07-29 08:58:09.081 | 68623b4237b14447a3c52184c83941dc | WARNING  | module_stream.service.task_execution_service:_validate_required_config:640 - 算法 car_counting 缺少配置参数: 置信度阈值 (confidence_threshold 或 conf_thres), NMS阈值 (nms_threshold 或 nms_thres), 输入图像尺寸 (input_size 或 img_size)。将使用默认值，建议在算法配置页面设置这些参数以获得更好的检测效果。
2025-07-29 08:58:09.081 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:664 - 验证模型初始化 - 添加YOLOv5路径: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master
2025-07-29 08:58:09.082 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:679 - 验证模型初始化 - 成功预导入YOLOv5 utils模块
2025-07-29 08:58:09.082 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:684 - 验证模型初始化 - 当前sys.path前5项: ['D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master\\utils', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master', 'D:\\ai-recognition\\RuoYi-Vue3-FastAPI-master\\ruoyi-fastapi-backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs']
2025-07-29 08:58:09.083 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:685 - 验证模型初始化 - 当前工作目录: D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend
2025-07-29 08:58:09.083 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:689 - 验证模型初始化 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-29 08:58:10.320 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:710 - 验证模型初始化 - 成功导入智驱力模型
2025-07-29 08:58:10.320 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-29 08:58:10.320 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-29 08:58:10.320 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-29 08:58:10.321 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 08:58:10.321 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:722 - 验证模型初始化 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 08:58:12.477 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:734 - 验证模型初始化 - 智驱力模型初始化成功
2025-07-29 08:58:12.478 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:735 -    - 设备: cuda
2025-07-29 08:58:12.478 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:736 -    - 图像尺寸: 640
2025-07-29 08:58:12.478 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:737 -    - 置信度阈值: 0.01
2025-07-29 08:58:12.478 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:738 -    - NMS阈值: 0.5
2025-07-29 08:58:12.483 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:751 - 验证模型初始化 - 智驱力后处理器初始化成功
2025-07-29 08:58:12.498 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:start_monitor_stream:4094 - 任务 12 的监控流已启动
2025-07-29 08:58:12.500 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_cache_task_config:112 - 任务12配置已缓存
2025-07-29 08:58:12.500 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:detection_loop:799 - 任务12配置缓存完成: 区域1个, 线段0个
2025-07-29 08:58:12.501 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:detection_loop:829 - 成功预导入YOLOv5 utils模块
2025-07-29 08:58:12.501 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:detection_loop:835 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-29 08:58:12.502 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: zql_detect
2025-07-29 08:58:12.503 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: model
2025-07-29 08:58:12.503 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:detection_loop:855 - 成功导入智驱力模型
2025-07-29 08:58:12.503 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-29 08:58:12.503 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-29 08:58:12.503 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-29 08:58:12.503 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 08:58:12.503 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:detection_loop:867 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 08:58:12.617 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:detection_loop:880 - 智驱力模型初始化成功
2025-07-29 08:58:12.617 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:detection_loop:881 -    - 设备: cuda
2025-07-29 08:58:12.617 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:detection_loop:882 -    - 图像尺寸: 640
2025-07-29 08:58:12.617 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:detection_loop:883 -    - 置信度阈值: 0.01
2025-07-29 08:58:12.617 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:detection_loop:884 -    - NMS阈值: 0.5
2025-07-29 08:58:12.618 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:detection_loop:897 - 智驱力后处理器初始化成功
2025-07-29 08:58:12.686 | 68623b4237b14447a3c52184c83941dc | ERROR    | module_stream.service.task_execution_service:detection_loop:917 - 视频流连接超时或失败: rtsp://127.0.0.1:8554/test1
2025-07-29 08:58:12.689 | 68623b4237b14447a3c52184c83941dc | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3287 - 更新任务状态失败: readexactly() called while another coroutine is already waiting for incoming data
2025-07-29 08:58:12.689 | 68623b4237b14447a3c52184c83941dc | ERROR    | module_stream.service.task_execution_service:detection_loop:1280 - 检测过程中发生错误: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-07-29 08:58:12.689 | 68623b4237b14447a3c52184c83941dc | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3287 - 更新任务状态失败: This session is in 'prepared' state; no further SQL can be emitted within this transaction.
2025-07-29 08:58:12.692 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.service.task_execution_service:start_task:255 - 任务 12 启动成功，包括实时监控流
2025-07-29 08:58:12.692 | 68623b4237b14447a3c52184c83941dc | INFO     | module_stream.controller.monitor_controller:batch_start_tasks:157 - 批量启动任务完全成功: [12]
2025-07-29 08:58:12.704 | 75a654bf805d49dc85d53d246ee03b80 | ERROR    | exceptions.handle:exception_handler:117 - 数据库连接错误: (asyncmy.errors.OperationalError) (2014, 'Command Out of Sync')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-29 08:58:19.701 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-29 08:58:19.702 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
