2025-07-29 08:41:37.405 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-29 08:41:37.405 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-29 08:41:37.436 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-29 08:41:37.436 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-29 08:41:37.437 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-29 08:41:37.477 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-29 08:41:37.500 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-29 08:41:37.500 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-29 08:41:38.029 | 1ce0fd84906d47d4b6774b568b173e4d | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-29 08:41:38.046 | 32076af5e14f4769a3f5ddf19cce12a1 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-29 08:41:38.229 | fa9df2669c6a4cb9af64ae2716702a30 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-29 08:41:43.303 | 05b99723467b4b829e795e69a725ed48 | INFO     | module_alert.controller.alert_controller:get_alert_manage_alert_list:70 - 获取成功
2025-07-29 08:41:53.490 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1866: 实体对象 = True
2025-07-29 08:41:53.491 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083850_077.jpg
2025-07-29 08:41:53.491 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083850_077.jpg
2025-07-29 08:41:53.493 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1866
2025-07-29 08:41:53.494 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1865: 实体对象 = True
2025-07-29 08:41:53.495 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083849_514.jpg
2025-07-29 08:41:53.495 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083849_514.jpg
2025-07-29 08:41:53.496 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1865
2025-07-29 08:41:53.497 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1864: 实体对象 = True
2025-07-29 08:41:53.498 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083848_508.jpg
2025-07-29 08:41:53.498 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083848_508.jpg
2025-07-29 08:41:53.499 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1864
2025-07-29 08:41:53.500 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1863: 实体对象 = True
2025-07-29 08:41:53.501 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083847_725.jpg
2025-07-29 08:41:53.501 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083847_725.jpg
2025-07-29 08:41:53.502 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1863
2025-07-29 08:41:53.504 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1862: 实体对象 = True
2025-07-29 08:41:53.504 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083845_655.jpg
2025-07-29 08:41:53.505 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083845_655.jpg
2025-07-29 08:41:53.506 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1862
2025-07-29 08:41:53.507 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1861: 实体对象 = True
2025-07-29 08:41:53.508 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083844_820.jpg
2025-07-29 08:41:53.508 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083844_820.jpg
2025-07-29 08:41:53.509 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1861
2025-07-29 08:41:53.510 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1860: 实体对象 = True
2025-07-29 08:41:53.511 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083843_477.jpg
2025-07-29 08:41:53.511 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083843_477.jpg
2025-07-29 08:41:53.512 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1860
2025-07-29 08:41:53.513 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1859: 实体对象 = True
2025-07-29 08:41:53.514 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083841_530.jpg
2025-07-29 08:41:53.514 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083841_530.jpg
2025-07-29 08:41:53.514 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1859
2025-07-29 08:41:53.516 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1858: 实体对象 = True
2025-07-29 08:41:53.516 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083840_953.jpg
2025-07-29 08:41:53.516 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083840_953.jpg
2025-07-29 08:41:53.517 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1858
2025-07-29 08:41:53.519 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1857: 实体对象 = True
2025-07-29 08:41:53.519 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083840_373.jpg
2025-07-29 08:41:53.519 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083840_373.jpg
2025-07-29 08:41:53.520 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1857
2025-07-29 08:41:53.521 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1856: 实体对象 = True
2025-07-29 08:41:53.522 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083839_793.jpg
2025-07-29 08:41:53.522 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083839_793.jpg
2025-07-29 08:41:53.523 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1856
2025-07-29 08:41:53.524 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1855: 实体对象 = True
2025-07-29 08:41:53.525 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083839_227.jpg
2025-07-29 08:41:53.525 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083839_227.jpg
2025-07-29 08:41:53.526 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1855
2025-07-29 08:41:53.527 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1854: 实体对象 = True
2025-07-29 08:41:53.527 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083838_644.jpg
2025-07-29 08:41:53.527 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083838_644.jpg
2025-07-29 08:41:53.529 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1854
2025-07-29 08:41:53.530 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1853: 实体对象 = True
2025-07-29 08:41:53.530 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083838_058.jpg
2025-07-29 08:41:53.530 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083838_058.jpg
2025-07-29 08:41:53.531 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1853
2025-07-29 08:41:53.532 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1852: 实体对象 = True
2025-07-29 08:41:53.533 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083837_487.jpg
2025-07-29 08:41:53.533 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083837_487.jpg
2025-07-29 08:41:53.534 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1852
2025-07-29 08:41:53.536 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1851: 实体对象 = True
2025-07-29 08:41:53.536 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083836_858.jpg
2025-07-29 08:41:53.536 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083836_858.jpg
2025-07-29 08:41:53.537 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1851
2025-07-29 08:41:53.538 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1850: 实体对象 = True
2025-07-29 08:41:53.539 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083836_270.jpg
2025-07-29 08:41:53.539 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083836_270.jpg
2025-07-29 08:41:53.540 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1850
2025-07-29 08:41:53.541 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1849: 实体对象 = True
2025-07-29 08:41:53.541 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083835_690.jpg
2025-07-29 08:41:53.542 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083835_690.jpg
2025-07-29 08:41:53.543 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1849
2025-07-29 08:41:53.544 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1848: 实体对象 = True
2025-07-29 08:41:53.544 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083835_011.jpg
2025-07-29 08:41:53.544 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083835_011.jpg
2025-07-29 08:41:53.545 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1848
2025-07-29 08:41:53.546 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1847: 实体对象 = True
2025-07-29 08:41:53.547 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083834_458.jpg
2025-07-29 08:41:53.547 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083834_458.jpg
2025-07-29 08:41:53.548 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1847
2025-07-29 08:41:53.549 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1846: 实体对象 = True
2025-07-29 08:41:53.549 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083833_898.jpg
2025-07-29 08:41:53.549 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083833_898.jpg
2025-07-29 08:41:53.550 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1846
2025-07-29 08:41:53.551 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1845: 实体对象 = True
2025-07-29 08:41:53.552 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083833_342.jpg
2025-07-29 08:41:53.552 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083833_342.jpg
2025-07-29 08:41:53.553 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1845
2025-07-29 08:41:53.554 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1844: 实体对象 = True
2025-07-29 08:41:53.555 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083832_804.jpg
2025-07-29 08:41:53.555 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083832_804.jpg
2025-07-29 08:41:53.556 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1844
2025-07-29 08:41:53.557 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1843: 实体对象 = True
2025-07-29 08:41:53.557 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083832_258.jpg
2025-07-29 08:41:53.557 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083832_258.jpg
2025-07-29 08:41:53.558 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1843
2025-07-29 08:41:53.560 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1842: 实体对象 = True
2025-07-29 08:41:53.560 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083831_733.jpg
2025-07-29 08:41:53.561 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083831_733.jpg
2025-07-29 08:41:53.562 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1842
2025-07-29 08:41:53.563 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1841: 实体对象 = True
2025-07-29 08:41:53.563 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083831_207.jpg
2025-07-29 08:41:53.563 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083831_207.jpg
2025-07-29 08:41:53.564 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1841
2025-07-29 08:41:53.566 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1840: 实体对象 = True
2025-07-29 08:41:53.566 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083830_673.jpg
2025-07-29 08:41:53.566 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083830_673.jpg
2025-07-29 08:41:53.567 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1840
2025-07-29 08:41:53.568 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1839: 实体对象 = True
2025-07-29 08:41:53.569 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083830_156.jpg
2025-07-29 08:41:53.569 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083830_156.jpg
2025-07-29 08:41:53.570 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1839
2025-07-29 08:41:53.571 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1838: 实体对象 = True
2025-07-29 08:41:53.572 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083829_640.jpg
2025-07-29 08:41:53.572 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083829_640.jpg
2025-07-29 08:41:53.573 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1838
2025-07-29 08:41:53.574 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1837: 实体对象 = True
2025-07-29 08:41:53.574 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083829_126.jpg
2025-07-29 08:41:53.575 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083829_126.jpg
2025-07-29 08:41:53.576 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1837
2025-07-29 08:41:53.577 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1836: 实体对象 = True
2025-07-29 08:41:53.577 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083828_622.jpg
2025-07-29 08:41:53.578 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083828_622.jpg
2025-07-29 08:41:53.579 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1836
2025-07-29 08:41:53.580 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1835: 实体对象 = True
2025-07-29 08:41:53.580 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083828_107.jpg
2025-07-29 08:41:53.580 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083828_107.jpg
2025-07-29 08:41:53.581 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1835
2025-07-29 08:41:53.583 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1834: 实体对象 = True
2025-07-29 08:41:53.583 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083827_594.jpg
2025-07-29 08:41:53.583 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083827_594.jpg
2025-07-29 08:41:53.584 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1834
2025-07-29 08:41:53.586 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1833: 实体对象 = True
2025-07-29 08:41:53.586 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083827_071.jpg
2025-07-29 08:41:53.586 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083827_071.jpg
2025-07-29 08:41:53.587 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1833
2025-07-29 08:41:53.589 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1832: 实体对象 = True
2025-07-29 08:41:53.589 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083826_553.jpg
2025-07-29 08:41:53.589 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083826_553.jpg
2025-07-29 08:41:53.591 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1832
2025-07-29 08:41:53.592 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1831: 实体对象 = True
2025-07-29 08:41:53.592 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083826_032.jpg
2025-07-29 08:41:53.593 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083826_032.jpg
2025-07-29 08:41:53.594 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1831
2025-07-29 08:41:53.595 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1830: 实体对象 = True
2025-07-29 08:41:53.595 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083825_523.jpg
2025-07-29 08:41:53.596 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083825_523.jpg
2025-07-29 08:41:53.597 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1830
2025-07-29 08:41:53.598 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1829: 实体对象 = True
2025-07-29 08:41:53.598 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083825_016.jpg
2025-07-29 08:41:53.599 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083825_016.jpg
2025-07-29 08:41:53.600 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1829
2025-07-29 08:41:53.602 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1828: 实体对象 = True
2025-07-29 08:41:53.602 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083824_514.jpg
2025-07-29 08:41:53.603 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083824_514.jpg
2025-07-29 08:41:53.604 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1828
2025-07-29 08:41:53.606 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1827: 实体对象 = True
2025-07-29 08:41:53.606 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083824_003.jpg
2025-07-29 08:41:53.606 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083824_003.jpg
2025-07-29 08:41:53.608 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1827
2025-07-29 08:41:53.609 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1826: 实体对象 = True
2025-07-29 08:41:53.610 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083823_487.jpg
2025-07-29 08:41:53.610 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083823_487.jpg
2025-07-29 08:41:53.611 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1826
2025-07-29 08:41:53.613 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1825: 实体对象 = True
2025-07-29 08:41:53.613 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083822_975.jpg
2025-07-29 08:41:53.613 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083822_975.jpg
2025-07-29 08:41:53.615 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1825
2025-07-29 08:41:53.616 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1824: 实体对象 = True
2025-07-29 08:41:53.616 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083822_476.jpg
2025-07-29 08:41:53.617 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083822_476.jpg
2025-07-29 08:41:53.617 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1824
2025-07-29 08:41:53.619 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1823: 实体对象 = True
2025-07-29 08:41:53.619 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083821_986.jpg
2025-07-29 08:41:53.619 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083821_986.jpg
2025-07-29 08:41:53.621 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1823
2025-07-29 08:41:53.622 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1822: 实体对象 = True
2025-07-29 08:41:53.622 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083821_488.jpg
2025-07-29 08:41:53.622 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083821_488.jpg
2025-07-29 08:41:53.623 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1822
2025-07-29 08:41:53.624 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1821: 实体对象 = True
2025-07-29 08:41:53.625 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083821_061.jpg
2025-07-29 08:41:53.625 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083821_061.jpg
2025-07-29 08:41:53.627 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1821
2025-07-29 08:41:53.628 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1820: 实体对象 = True
2025-07-29 08:41:53.628 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083820_708.jpg
2025-07-29 08:41:53.628 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083820_708.jpg
2025-07-29 08:41:53.629 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1820
2025-07-29 08:41:53.631 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1819: 实体对象 = True
2025-07-29 08:41:53.631 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083820_352.jpg
2025-07-29 08:41:53.631 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083820_352.jpg
2025-07-29 08:41:53.633 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1819
2025-07-29 08:41:53.634 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1818: 实体对象 = True
2025-07-29 08:41:53.634 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083820_000.jpg
2025-07-29 08:41:53.635 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083820_000.jpg
2025-07-29 08:41:53.636 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1818
2025-07-29 08:41:53.637 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:175 - 检查告警记录 1817: 实体对象 = True
2025-07-29 08:41:53.637 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:_delete_alert_screenshot:273 - 成功删除截图文件: static\alert_screenshots\alert_12_8_20250729_083819_649.jpg
2025-07-29 08:41:53.637 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:183 - 成功删除告警截图: /static/alert_screenshots/alert_12_8_20250729_083819_649.jpg
2025-07-29 08:41:53.639 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.service.alert_service:delete_alert_services:195 - 成功删除告警记录 1817
2025-07-29 08:41:53.642 | 3c07e5ec86b54448b4ab11b954f0dd26 | INFO     | module_alert.controller.alert_controller:delete_alert_manage_alert:118 - 成功删除50条记录，删除50个截图文件
2025-07-29 08:41:53.652 | 3c07e5ec86b54448b4ab11b954f0dd26 | ERROR    | exceptions.handle:exception_handler:136 - (asyncmy.errors.DataError) (1406, "Data too long for column 'oper_url' at row 1")
[SQL: INSERT INTO sys_oper_log (title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, error_msg, oper_time, cost_time) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)]
[parameters: ('警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/1866,1865,1864,1863,1862,1861,1860,1859,1858,1857,1856,1855,1854,1853,1852,1851,1850,1849,1848,1847,1846,1845,1844,1843,1842,1841,1840,1839,1838,1837,1836,1835,1834,1833,1832,1831,1830,1829,1828,1827,1826,1825,1824,1823,1822,1821,1820,1819,1818,1817', '', '未知', '{"alert_ids": "1866,1865,1864,1863,1862,1861,1860,1859,1858,1857,1856,1855,1854,1853,1852,1851,1850,1849,1848,1847,1846,1845,1844,1843,1842,1841,1840,1839,1838,1837,1836,1835,1834,1833,1832,1831,1830,1829,1828,1827,1826,1825,1824,1823,1822,1821,1820,1819,1818,1817"}', '{"code": 200, "msg": "成功删除50条记录，删除50个截图文件", "success": true, "time": "2025-07-29T08:41:53.642977"}', 0, '', datetime.datetime(2025, 7, 29, 8, 41, 53, 483644), 26)]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
Traceback (most recent call last):

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x000001E767B576A0>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x000001E7682AA3C0>
    └ <sqlalchemy.engine.base.Connection object at 0x000001E713BC2B60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ('警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/s...
    │      │       └ 'INSERT INTO sys_oper_log (title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x000001E7681B8F40>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001E7139FA660>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 95, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ('警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/s...
           │    │      │    │              └ 'INSERT INTO sys_oper_log (title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x000001E7681B9080>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001E7139FA660>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001E7139FA660>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           │       │             └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000001E713B9E240>
           │       └ <attribute 'parent' of 'greenlet.greenlet' objects>
           └ <_AsyncIoGreenlet object at 0x000001E713A83400 (otid=0x000001E76651AA00) dead>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <_cython_3_1_2.coroutine object at 0x000001E7139A5B40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 107, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ('警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/s...
                   │    │               └ 'INSERT INTO sys_oper_log (title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001E7139FA660>
  File "asyncmy/cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy/cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy/connection.pyx", line 496, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy/connection.pyx", line 684, in _read_query_result
    await result.read()
  File "asyncmy/connection.pyx", line 1071, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy/connection.pyx", line 646, in read_packet
    packet.raise_for_error()
  File "asyncmy/protocol.pyx", line 190, in asyncmy.protocol.MysqlPacket.raise_for_error
    cpdef raise_for_error(self):
  File "asyncmy/protocol.pyx", line 194, in asyncmy.protocol.MysqlPacket.raise_for_error
    errors.raise_mysql_exception(self._data)
    │      └ <cyfunction raise_mysql_exception at 0x000001E76816E380>
    └ <module 'asyncmy.errors' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\asyncmy\\err...
  File "asyncmy/errors.pyx", line 128, in asyncmy.errors.raise_mysql_exception
    cpdef raise_mysql_exception(bytes data):
          └ <cyfunction raise_mysql_exception at 0x000001E76816E380>
  File "asyncmy/errors.pyx", line 137, in asyncmy.errors.raise_mysql_exception
    raise error_class(errno, err_val)

asyncmy.errors.DataError: (1406, "Data too long for column 'oper_url' at row 1")


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 276
               │     └ 3
               └ <function _main at 0x000001E7631756C0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 276
           │    └ <function BaseProcess._bootstrap at 0x000001E762E84900>
           └ <SpawnProcess name='SpawnProcess-1' parent=13220 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 313, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x000001E762E83E20>
    └ <SpawnProcess name='SpawnProcess-1' parent=13220 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\multiprocessing\process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x000001E762E5ECF0>, 'target': <bound method Server.run of <uvicorn.server.Server...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=13220 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=13220 started>
    │    └ <function subprocess_started at 0x000001E76536C040>
    └ <SpawnProcess name='SpawnProcess-1' parent=13220 started>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=1468, family=2, type=1, proto=6, laddr=('0.0.0.0', 9099)>]
    └ <bound method Server.run of <uvicorn.server.Server object at 0x000001E7135686E0>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=1468, family=2, type=1, proto=6, laddr=('0.0.0.0', 9099)>]
           │       │   │    └ <function Server.serve at 0x000001E765367060>
           │       │   └ <uvicorn.server.Server object at 0x000001E7135686E0>
           │       └ <function run at 0x000001E764FD0A40>
           └ <module 'asyncio' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\asyncio\\__init__.py'>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x000001E7138B3760>
           │      └ <function Runner.run at 0x000001E765010040>
           └ <asyncio.runners.Runner object at 0x000001E713569160>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-pa...
           │    │     └ <function BaseEventLoop.run_until_complete at 0x000001E765009A80>
           │    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x000001E713569160>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 712, in run_until_complete
    self.run_forever()
    │    └ <function BaseEventLoop.run_forever at 0x000001E7650099E0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 683, in run_forever
    self._run_once()
    │    └ <function BaseEventLoop._run_once at 0x000001E76500B7E0>
    └ <_WindowsSelectorEventLoop running=True closed=False debug=False>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\base_events.py", line 2042, in _run_once
    handle._run()
    │      └ <function Handle._run at 0x000001E764B814E0>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\asyncio\events.py", line 89, in _run
    self._context.run(self._callback, *self._args)
    │    │            │    │           │    └ <member '_args' of 'Handle' objects>
    │    │            │    │           └ <Handle Task.task_wakeup()>
    │    │            │    └ <member '_callback' of 'Handle' objects>
    │    │            └ <Handle Task.task_wakeup()>
    │    └ <member '_context' of 'Handle' objects>
    └ <Handle Task.task_wakeup()>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\protocols\http\httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001E71356AE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\uvicorn\middleware\proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E713...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
                 │    └ <fastapi.applications.FastAPI object at 0x000001E712FCFCB0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x000001E71356AE40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E713...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E713...
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001E7139E0590>
          └ <fastapi.applications.FastAPI object at 0x000001E712FCFCB0>
> File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x000001E7139D2E80>
          │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x000001E...
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x000001E7139E0440>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x000001E7139E0590>

  File "D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend\middlewares\trace_middleware\middle.py", line 48, in __call__
    await self.app(scope, handle_outgoing_receive, handle_outgoing_request)
          │    │   │      │                        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x000001E713AFC040>
          │    │   │      └ <function RequestResponseCycle.receive at 0x000001E713AFC220>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.gzip.GZipMiddleware object at 0x000001E7139E02F0>
          └ <middlewares.trace_middleware.middle.TraceASGIMiddleware object at 0x000001E7139E0440>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\gzip.py", line 20, in __call__
    await responder(scope, receive, send)
          │         │      │        └ <function TraceASGIMiddleware.__call__.<locals>.handle_outgoing_request at 0x000001E713AFC040>
          │         │      └ <function RequestResponseCycle.receive at 0x000001E713AFC220>
          │         └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <starlette.middleware.gzip.GZipResponder object at 0x000001E71399B950>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\gzip.py", line 39, in __call__
    await self.app(scope, receive, self.send_with_gzip)
          │    │   │      │        │    └ <function GZipResponder.send_with_gzip at 0x000001E77FD13E20>
          │    │   │      │        └ <starlette.middleware.gzip.GZipResponder object at 0x000001E71399B950>
          │    │   │      └ <function RequestResponseCycle.receive at 0x000001E713AFC220>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.cors.CORSMiddleware object at 0x000001E7139E01A0>
          └ <starlette.middleware.gzip.GZipResponder object at 0x000001E71399B950>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
          │    │               │      │        │                     └ Headers({'host': '127.0.0.1:9099', 'connection': 'close', 'sec-ch-ua-platform': '"Windows"', 'authorization': 'Bearer eyJhbGc...
          │    │               │      │        └ <bound method GZipResponder.send_with_gzip of <starlette.middleware.gzip.GZipResponder object at 0x000001E71399B950>>
          │    │               │      └ <function RequestResponseCycle.receive at 0x000001E713AFC220>
          │    │               └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function CORSMiddleware.simple_response at 0x000001E77FD13880>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001E7139E01A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
          │    │   │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001E7139E01A0...
          │    │   │      └ <function RequestResponseCycle.receive at 0x000001E713AFC220>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001E7139E0050>
          └ <starlette.middleware.cors.CORSMiddleware object at 0x000001E7139E01A0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\middleware\exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ functools.partial(<bound method CORSMiddleware.send of <starlette.middleware.cors.CORSMiddleware object at 0x000001E7139E01A0...
          │                            │    │    │     │      └ <function RequestResponseCycle.receive at 0x000001E713AFC220>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    │    └ <starlette.requests.Request object at 0x000001E713A5FE50>
          │                            │    └ <fastapi.routing.APIRouter object at 0x000001E713561220>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x000001E7139E0050>
          └ <function wrap_app_handling_exceptions at 0x000001E766346520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E713B3E7A0>
          │   │      └ <function RequestResponseCycle.receive at 0x000001E713AFC220>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <fastapi.routing.APIRouter object at 0x000001E713561220>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E713B3E7A0>
          │    │                │      └ <function RequestResponseCycle.receive at 0x000001E713AFC220>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x000001E713561220>>
          └ <fastapi.routing.APIRouter object at 0x000001E713561220>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E713B3E7A0>
          │     │      │      └ <function RequestResponseCycle.receive at 0x000001E713AFC220>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │     └ <function Route.handle at 0x000001E766347D80>
          └ APIRoute(path='/surveillance/alert/{alert_ids}', name='delete_alert_manage_alert', methods=['DELETE'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E713B3E7A0>
          │    │   │      └ <function RequestResponseCycle.receive at 0x000001E713AFC220>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │    └ <function request_response.<locals>.app at 0x000001E7138D8D60>
          └ APIRoute(path='/surveillance/alert/{alert_ids}', name='delete_alert_manage_alert', methods=['DELETE'])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E713B3E7A0>
          │                            │    │        │      └ <function RequestResponseCycle.receive at 0x000001E713AFC220>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          │                            │    └ <starlette.requests.Request object at 0x000001E713B9D550>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x000001E713ADBF60>
          └ <function wrap_app_handling_exceptions at 0x000001E766346520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x000001E713AD9120>
          │   │      └ <function RequestResponseCycle.receive at 0x000001E713AFC220>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 9099), 'cl...
          └ <function request_response.<locals>.app.<locals>.app at 0x000001E713ADBF60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\starlette\routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x000001E713B9D550>
                     └ <function get_request_handler.<locals>.app at 0x000001E7138D8C20>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x000001E766345D00>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\fastapi\routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'query_db': <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000001E7139C7460>, 'current_user': CurrentUserModel(per...
                 │         └ <function delete_alert_manage_alert at 0x000001E7135093A0>
                 └ Dependant(path_params=[ModelField(field_info=Path(PydanticUndefined), name='alert_ids', mode='validation')], query_params=[],...

  File "D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend\module_admin\annotation\log_annotation.py", line 199, in wrapper
    await OperationLogService.add_operation_log_services(query_db, operation_log)
          │                   │                          │         └ OperLogModel(oper_id=None, title='警告记录', business_type=3, method='module_alert.controller.alert_controller.delete_alert_manag...
          │                   │                          └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000001E7139C7460>
          │                   └ <classmethod(<function OperationLogService.add_operation_log_services at 0x000001E71177D6C0>)>
          └ <class 'module_admin.service.log_service.OperationLogService'>

  File "D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend\module_admin\service\log_service.py", line 56, in add_operation_log_services
    raise e

  File "D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend\module_admin\service\log_service.py", line 51, in add_operation_log_services
    await OperationLogDao.add_operation_log_dao(query_db, page_object)
          │               │                     │         └ OperLogModel(oper_id=None, title='警告记录', business_type=3, method='module_alert.controller.alert_controller.delete_alert_manag...
          │               │                     └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000001E7139C7460>
          │               └ <classmethod(<function OperationLogDao.add_operation_log_dao at 0x000001E71177D8A0>)>
          └ <class 'module_admin.dao.log_dao.OperationLogDao'>

  File "D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend\module_admin\dao\log_dao.py", line 66, in add_operation_log_dao
    await db.flush()
          │  └ <function AsyncSession.flush at 0x000001E76812ED40>
          └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000001E7139C7460>

  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\ext\asyncio\session.py", line 802, in flush
    await greenlet_spawn(self.sync_session.flush, objects=objects)
          │              │    │            │              └ None
          │              │    │            └ <function Session.flush at 0x000001E767E8C900>
          │              │    └ <sqlalchemy.orm.session.Session object at 0x000001E7139C7570>
          │              └ <sqlalchemy.ext.asyncio.session.AsyncSession object at 0x000001E7139C7460>
          └ <function greenlet_spawn at 0x000001E766566520>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 203, in greenlet_spawn
    result = context.switch(value)
             │       │      └ None
             │       └ <method 'switch' of 'greenlet.greenlet' objects>
             └ <_AsyncIoGreenlet object at 0x000001E713A83400 (otid=0x000001E76651AA00) dead>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 4353, in flush
    self._flush(objects)
    │    │      └ None
    │    └ <function Session._flush at 0x000001E767E8CAE0>
    └ <sqlalchemy.orm.session.Session object at 0x000001E7139C7570>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 4488, in _flush
    with util.safe_reraise():
         │    └ <class 'sqlalchemy.util.langhelpers.safe_reraise'>
         └ <module 'sqlalchemy.util' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\sqlalchemy\...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
          │         │              └ <traceback object at 0x000001E713C12C00>
          │         └ <method 'with_traceback' of 'BaseException' objects>
          └ DataError('(asyncmy.errors.DataError) (1406, "Data too long for column \'oper_url\' at row 1")')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\session.py", line 4449, in _flush
    flush_context.execute()
    │             └ <function UOWTransaction.execute at 0x000001E767E69260>
    └ <sqlalchemy.orm.unitofwork.UOWTransaction object at 0x000001E713BA1160>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
    │   │       └ <sqlalchemy.orm.unitofwork.UOWTransaction object at 0x000001E713BA1160>
    │   └ <function SaveUpdateAll.execute at 0x000001E767E69DA0>
    └ SaveUpdateAll(Mapper[SysOperLog(sys_oper_log)])
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
    │    │         │               └ <function save_obj at 0x000001E767DF36A0>
    │    │         └ <module 'sqlalchemy.orm.persistence' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\...
    │    └ <module 'sqlalchemy.util.preloaded' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\s...
    └ <module 'sqlalchemy.util' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\sqlalchemy\...
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\persistence.py", line 93, in save_obj
    _emit_insert_statements(
    └ <function _emit_insert_statements at 0x000001E767E580E0>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\orm\persistence.py", line 1233, in _emit_insert_statements
    result = connection.execute(
             │          └ <function Connection.execute at 0x000001E767AC4040>
             └ <sqlalchemy.engine.base.Connection object at 0x000001E713BC2B60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1416, in execute
    return meth(
           └ <bound method ClauseElement._execute_on_connection of <sqlalchemy.sql.dml.Insert object at 0x000001E713BA17F0>>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\sql\elements.py", line 516, in _execute_on_connection
    return connection._execute_clauseelement(
           │          └ <function Connection._execute_clauseelement at 0x000001E767AC4360>
           └ <sqlalchemy.engine.base.Connection object at 0x000001E713BC2B60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
          │    └ <function Connection._execute_context at 0x000001E767AC4540>
          └ <sqlalchemy.engine.base.Connection object at 0x000001E713BC2B60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1843, in _execute_context
    return self._exec_single_context(
           │    └ <function Connection._exec_single_context at 0x000001E767AC45E0>
           └ <sqlalchemy.engine.base.Connection object at 0x000001E713BC2B60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1983, in _exec_single_context
    self._handle_dbapi_exception(
    │    └ <function Connection._handle_dbapi_exception at 0x000001E767AC4860>
    └ <sqlalchemy.engine.base.Connection object at 0x000001E713BC2B60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
          │                    │              │                 └ DataError(1406, "Data too long for column 'oper_url' at row 1")
          │                    │              └ (<class 'asyncmy.errors.DataError'>, DataError(1406, "Data too long for column 'oper_url' at row 1"), <traceback object at 0x...
          │                    └ <method 'with_traceback' of 'BaseException' objects>
          └ DataError('(asyncmy.errors.DataError) (1406, "Data too long for column \'oper_url\' at row 1")')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\base.py", line 1964, in _exec_single_context
    self.dialect.do_execute(
    │    │       └ <function DefaultDialect.do_execute at 0x000001E767B576A0>
    │    └ <sqlalchemy.dialects.mysql.asyncmy.MySQLDialect_asyncmy object at 0x000001E7682AA3C0>
    └ <sqlalchemy.engine.base.Connection object at 0x000001E713BC2B60>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\engine\default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
    │      │       │          └ ('警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/s...
    │      │       └ 'INSERT INTO sys_oper_log (title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_...
    │      └ <function AsyncAdapt_asyncmy_cursor.execute at 0x000001E7681B8F40>
    └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001E7139FA660>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 95, in execute
    return self.await_(self._execute_async(operation, parameters))
           │    │      │    │              │          └ ('警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/s...
           │    │      │    │              └ 'INSERT INTO sys_oper_log (title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_...
           │    │      │    └ <function AsyncAdapt_asyncmy_cursor._execute_async at 0x000001E7681B9080>
           │    │      └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001E7139FA660>
           │    └ <member 'await_' of 'AsyncAdapt_asyncmy_cursor' objects>
           └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001E7139FA660>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 132, in await_only
    return current.parent.switch(awaitable)  # type: ignore[no-any-return,attr-defined] # noqa: E501
           │       │             └ <coroutine object AsyncAdapt_asyncmy_cursor._execute_async at 0x000001E713B9E240>
           │       └ <attribute 'parent' of 'greenlet.greenlet' objects>
           └ <_AsyncIoGreenlet object at 0x000001E713A83400 (otid=0x000001E76651AA00) dead>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\util\_concurrency_py3k.py", line 196, in greenlet_spawn
    value = await result
                  └ <_cython_3_1_2.coroutine object at 0x000001E7139A5B40>
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\sqlalchemy\dialects\mysql\asyncmy.py", line 107, in _execute_async
    result = await self._cursor.execute(operation, parameters)
                   │    │               │          └ ('警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/s...
                   │    │               └ 'INSERT INTO sys_oper_log (title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_...
                   │    └ <member '_cursor' of 'AsyncAdapt_asyncmy_cursor' objects>
                   └ <sqlalchemy.dialects.mysql.asyncmy.AsyncAdapt_asyncmy_cursor object at 0x000001E7139FA660>
  File "asyncmy/cursors.pyx", line 179, in execute
    result = await self._query(query)
  File "asyncmy/cursors.pyx", line 364, in _query
    await conn.query(q)
  File "asyncmy/connection.pyx", line 496, in query
    await self._read_query_result(unbuffered=unbuffered)
  File "asyncmy/connection.pyx", line 684, in _read_query_result
    await result.read()
  File "asyncmy/connection.pyx", line 1071, in read
    first_packet = await self.connection.read_packet()
  File "asyncmy/connection.pyx", line 646, in read_packet
    packet.raise_for_error()
  File "asyncmy/protocol.pyx", line 190, in asyncmy.protocol.MysqlPacket.raise_for_error
    cpdef raise_for_error(self):
  File "asyncmy/protocol.pyx", line 194, in asyncmy.protocol.MysqlPacket.raise_for_error
    errors.raise_mysql_exception(self._data)
    │      └ <cyfunction raise_mysql_exception at 0x000001E76816E380>
    └ <module 'asyncmy.errors' from 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\asyncmy\\err...
  File "asyncmy/errors.pyx", line 128, in asyncmy.errors.raise_mysql_exception
    cpdef raise_mysql_exception(bytes data):
          └ <cyfunction raise_mysql_exception at 0x000001E76816E380>
  File "asyncmy/errors.pyx", line 137, in asyncmy.errors.raise_mysql_exception
    raise error_class(errno, err_val)

sqlalchemy.exc.DataError: (asyncmy.errors.DataError) (1406, "Data too long for column 'oper_url' at row 1")
[SQL: INSERT INTO sys_oper_log (title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, error_msg, oper_time, cost_time) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)]
[parameters: ('警告记录', 3, 'module_alert.controller.alert_controller.delete_alert_manage_alert()', 'DELETE', 1, 'admin', '研发部门', '/dev-api/surveillance/alert/1866,1865,1864,1863,1862,1861,1860,1859,1858,1857,1856,1855,1854,1853,1852,1851,1850,1849,1848,1847,1846,1845,1844,1843,1842,1841,1840,1839,1838,1837,1836,1835,1834,1833,1832,1831,1830,1829,1828,1827,1826,1825,1824,1823,1822,1821,1820,1819,1818,1817', '', '未知', '{"alert_ids": "1866,1865,1864,1863,1862,1861,1860,1859,1858,1857,1856,1855,1854,1853,1852,1851,1850,1849,1848,1847,1846,1845,1844,1843,1842,1841,1840,1839,1838,1837,1836,1835,1834,1833,1832,1831,1830,1829,1828,1827,1826,1825,1824,1823,1822,1821,1820,1819,1818,1817"}', '{"code": 200, "msg": "成功删除50条记录，删除50个截图文件", "success": true, "time": "2025-07-29T08:41:53.642977"}', 0, '', datetime.datetime(2025, 7, 29, 8, 41, 53, 483644), 26)]
(Background on this error at: https://sqlalche.me/e/20/9h9h)
2025-07-29 08:42:27.815 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-29 08:42:27.815 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
