2025-07-29 09:03:21.131 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-29 09:03:21.132 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-29 09:03:21.159 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-29 09:03:21.159 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-29 09:03:21.161 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-29 09:03:21.194 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-29 09:03:21.218 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-29 09:03:21.219 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-29 09:03:26.676 | 07b60c3cf1784391927825718b669f0f | INFO     | module_admin.controller.login_controller:get_login_user_info:84 - 获取成功
2025-07-29 09:03:26.690 | 8289538d17284728a45c8a8db01eb304 | INFO     | module_admin.controller.login_controller:get_login_user_routers:95 - 获取成功
2025-07-29 09:03:26.802 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-29 09:03:26.802 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 09:03:26.803 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 09:03:26.803 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:03:26.803 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 09:03:26.804 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 09:03:26.804 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 09:03:26.804 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:03:26.807 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 09:03:26.807 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 09:03:26.814 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-29 09:03:26.815 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 09:03:26.815 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-29 09:03:26.815 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-29 09:03:26.818 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 09:03:26.819 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-29 09:03:26.819 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-29 09:03:26.819 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 09:03:26.819 | 5fd0e5d436c849589cbc984624d9f125 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-29 09:03:29.527 | 4ccb83bd869642ef8f12616d63626f9e | INFO     | module_stream.service.task_execution_service:stop_task:306 - 开始停止任务: 12
2025-07-29 09:03:29.528 | 4ccb83bd869642ef8f12616d63626f9e | INFO     | module_stream.service.task_execution_service:stop_task:332 - 任务 12 不在运行任务列表中
2025-07-29 09:03:29.529 | 4ccb83bd869642ef8f12616d63626f9e | INFO     | module_stream.service.task_execution_service:stop_task:339 - 任务 12 使用智驱力直接集成，无需清理外部进程
2025-07-29 09:03:29.529 | 4ccb83bd869642ef8f12616d63626f9e | INFO     | module_stream.service.task_execution_service:stop_task:344 - 监控流停止成功: 12
2025-07-29 09:03:29.529 | 4ccb83bd869642ef8f12616d63626f9e | INFO     | module_stream.service.task_execution_service:_clear_task_cache:133 - 任务12缓存已清除
2025-07-29 09:03:29.529 | 4ccb83bd869642ef8f12616d63626f9e | INFO     | module_stream.service.task_execution_service:stop_task:353 - 任务缓存清理成功: 12
2025-07-29 09:03:29.539 | 4ccb83bd869642ef8f12616d63626f9e | INFO     | module_stream.service.task_execution_service:stop_task:362 - 任务状态更新成功: 12
2025-07-29 09:03:29.539 | 4ccb83bd869642ef8f12616d63626f9e | INFO     | module_stream.service.task_execution_service:stop_task:374 - 任务 12 停止成功，包括实时监控流
2025-07-29 09:03:29.540 | 4ccb83bd869642ef8f12616d63626f9e | INFO     | module_stream.controller.monitor_controller:batch_stop_tasks:209 - 批量停止任务成功: [12]
2025-07-29 09:03:29.557 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-29 09:03:29.558 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 09:03:29.558 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 09:03:29.558 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:03:29.558 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 09:03:29.558 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 09:03:29.559 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 09:03:29.559 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:03:29.559 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 09:03:29.559 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 09:03:29.563 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=2, rows=2
2025-07-29 09:03:29.563 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 09:03:29.563 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=2, rows=2
2025-07-29 09:03:29.563 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 2
2025-07-29 09:03:29.565 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 09:03:29.566 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 2 详情构建成功: 人员的入侵
2025-07-29 09:03:29.566 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 2
2025-07-29 09:03:29.566 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 09:03:29.566 | 46791a153da3465a886ac9f45cf394ca | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-29 09:03:33.104 | 385049c53ad243d4bcdb65d41bf24e44 | INFO     | module_stream.service.task_service:delete_task_services:191 - === TaskService.delete_task_services ===
2025-07-29 09:03:33.104 | 385049c53ad243d4bcdb65d41bf24e44 | INFO     | module_stream.service.task_service:delete_task_services:192 - 删除请求对象: task_ids='11'
2025-07-29 09:03:33.105 | 385049c53ad243d4bcdb65d41bf24e44 | INFO     | module_stream.service.task_service:delete_task_services:193 - 任务ID字符串: 11
2025-07-29 09:03:33.105 | 385049c53ad243d4bcdb65d41bf24e44 | INFO     | module_stream.service.task_service:delete_task_services:197 - 解析后的任务ID列表: ['11']
2025-07-29 09:03:33.107 | 385049c53ad243d4bcdb65d41bf24e44 | INFO     | module_stream.service.task_service:delete_task_services:204 - 任务 11 信息: 名称=人员的入侵, 状态=0
2025-07-29 09:03:33.107 | 385049c53ad243d4bcdb65d41bf24e44 | INFO     | module_stream.dao.task_dao:delete_task_dao:210 - === 删除任务操作 ===
2025-07-29 09:03:33.107 | 385049c53ad243d4bcdb65d41bf24e44 | INFO     | module_stream.dao.task_dao:delete_task_dao:211 - 要删除的任务ID列表: ['11']
2025-07-29 09:03:33.114 | 385049c53ad243d4bcdb65d41bf24e44 | INFO     | module_stream.dao.task_dao:delete_task_dao:219 - 删除操作完成，影响行数: 1
2025-07-29 09:03:33.114 | 385049c53ad243d4bcdb65d41bf24e44 | INFO     | module_stream.service.task_service:delete_task_services:215 - 删除操作完成，影响行数: 1
2025-07-29 09:03:33.117 | 385049c53ad243d4bcdb65d41bf24e44 | INFO     | module_stream.controller.task_controller:delete_task:93 - 删除成功
2025-07-29 09:03:33.213 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-29 09:03:33.213 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 09:03:33.214 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 09:03:33.214 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:03:33.214 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 09:03:33.214 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 09:03:33.214 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 09:03:33.215 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:03:33.215 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 09:03:33.215 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 09:03:33.218 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 09:03:33.218 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 09:03:33.218 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 09:03:33.218 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 09:03:33.220 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 09:03:33.220 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 09:03:33.220 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 09:03:33.220 | f99f30d501804666ad9470a4a37d9366 | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
2025-07-29 09:03:34.986 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:start_task:204 - 开始启动任务: 12
2025-07-29 09:03:34.988 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:498 - 加载算法配置: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info']
2025-07-29 09:03:34.988 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:505 - 加载检测区域配置: ['version', 'created_at', 'detection_areas', 'detection_lines', 'exclusion_areas']
2025-07-29 09:03:34.989 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:512 - 加载告警配置: ['version', 'created_at', 'alert_params']
2025-07-29 09:03:34.989 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:519 - 加载用户配置（优先级最高）: ['algorithm_id', 'custom_params', 'algorithm_name', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-29 09:03:34.989 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:525 - 成功加载数据库配置，包含字段: ['version', 'created_at', 'model_params', 'custom_params', 'algorithm_info', 'detection_areas', 'detection_lines', 'exclusion_areas', 'alert_params', 'algorithm_id', 'algorithm_name', 'alert_parameters', 'model_parameters', 'algorithm_version']
2025-07-29 09:03:34.989 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_load_algorithm_config:526 - 配置加载优先级: algorithm_config < bbox_config < alert_config < user_config
2025-07-29 09:03:34.989 | f317385d278b405bbcf0287078a6f39f | WARNING  | module_stream.service.task_execution_service:_validate_required_config:640 - 算法 car_counting 缺少配置参数: 置信度阈值 (confidence_threshold 或 conf_thres), NMS阈值 (nms_threshold 或 nms_thres), 输入图像尺寸 (input_size 或 img_size)。将使用默认值，建议在算法配置页面设置这些参数以获得更好的检测效果。
2025-07-29 09:03:34.989 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:664 - 验证模型初始化 - 添加YOLOv5路径: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master
2025-07-29 09:03:34.990 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:679 - 验证模型初始化 - 成功预导入YOLOv5 utils模块
2025-07-29 09:03:34.990 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:684 - 验证模型初始化 - 当前sys.path前5项: ['D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master\\utils', 'D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/01_ModelTraining/01_Detection/yolov5-master', 'D:\\ai-recognition\\RuoYi-Vue3-FastAPI-master\\ruoyi-fastapi-backend', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python313.zip', 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\DLLs']
2025-07-29 09:03:34.991 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:685 - 验证模型初始化 - 当前工作目录: D:\ai-recognition\RuoYi-Vue3-FastAPI-master\ruoyi-fastapi-backend
2025-07-29 09:03:34.991 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:689 - 验证模型初始化 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-29 09:03:36.201 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:710 - 验证模型初始化 - 成功导入智驱力模型
2025-07-29 09:03:36.202 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-29 09:03:36.202 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-29 09:03:36.202 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-29 09:03:36.202 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 09:03:36.202 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:722 - 验证模型初始化 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 09:03:38.461 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:734 - 验证模型初始化 - 智驱力模型初始化成功
2025-07-29 09:03:38.461 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:735 -    - 设备: cuda
2025-07-29 09:03:38.461 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:736 -    - 图像尺寸: 640
2025-07-29 09:03:38.461 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:737 -    - 置信度阈值: 0.01
2025-07-29 09:03:38.462 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:738 -    - NMS阈值: 0.5
2025-07-29 09:03:38.463 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_validate_model_initialization:751 - 验证模型初始化 - 智驱力后处理器初始化成功
2025-07-29 09:03:38.478 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:start_monitor_stream:4094 - 任务 12 的监控流已启动
2025-07-29 09:03:38.480 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_cache_task_config:112 - 任务12配置已缓存
2025-07-29 09:03:38.480 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:detection_loop:799 - 任务12配置缓存完成: 区域1个, 线段0个
2025-07-29 09:03:38.481 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:detection_loop:829 - 成功预导入YOLOv5 utils模块
2025-07-29 09:03:38.481 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:detection_loop:835 - 切换到算法包目录: D:/ai-recognition/Custom-Algorithm-main/Custom-Algorithm-main/02_CustomAlgorithm/02_Demo/KS968/car_counting
2025-07-29 09:03:38.482 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: zql_detect
2025-07-29 09:03:38.483 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:detection_loop:851 - 重新加载模块: model
2025-07-29 09:03:38.483 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:detection_loop:855 - 成功导入智驱力模型
2025-07-29 09:03:38.483 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 confidence_threshold: 0.01
2025-07-29 09:03:38.483 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 nms_threshold: 0.5
2025-07-29 09:03:38.483 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_get_config_value_with_priority:591 - 从model_params获取 input_size: 640
2025-07-29 09:03:38.484 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:_extract_model_parameters_from_config:478 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 09:03:38.484 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:detection_loop:867 - 最终模型配置: {'img_size': 640, 'conf_thres': 0.01, 'nms_thres': 0.5}
2025-07-29 09:03:38.604 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:detection_loop:880 - 智驱力模型初始化成功
2025-07-29 09:03:38.604 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:detection_loop:881 -    - 设备: cuda
2025-07-29 09:03:38.604 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:detection_loop:882 -    - 图像尺寸: 640
2025-07-29 09:03:38.604 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:detection_loop:883 -    - 置信度阈值: 0.01
2025-07-29 09:03:38.604 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:detection_loop:884 -    - NMS阈值: 0.5
2025-07-29 09:03:38.604 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:detection_loop:897 - 智驱力后处理器初始化成功
2025-07-29 09:03:38.669 | f317385d278b405bbcf0287078a6f39f | ERROR    | module_stream.service.task_execution_service:detection_loop:917 - 视频流连接超时或失败: rtsp://127.0.0.1:8554/test1
2025-07-29 09:03:38.671 | f317385d278b405bbcf0287078a6f39f | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3287 - 更新任务状态失败: readexactly() called while another coroutine is already waiting for incoming data
2025-07-29 09:03:38.672 | f317385d278b405bbcf0287078a6f39f | ERROR    | module_stream.service.task_execution_service:detection_loop:1280 - 检测过程中发生错误: Method 'rollback()' can't be called here; method 'commit()' is already in progress and this would cause an unexpected state change to <SessionTransactionState.CLOSED: 5> (Background on this error at: https://sqlalche.me/e/20/isce)
2025-07-29 09:03:38.672 | f317385d278b405bbcf0287078a6f39f | ERROR    | module_stream.service.task_execution_service:_update_task_status_with_error:3287 - 更新任务状态失败: This session is in 'prepared' state; no further SQL can be emitted within this transaction.
2025-07-29 09:03:38.674 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.service.task_execution_service:start_task:255 - 任务 12 启动成功，包括实时监控流
2025-07-29 09:03:38.674 | f317385d278b405bbcf0287078a6f39f | INFO     | module_stream.controller.monitor_controller:batch_start_tasks:157 - 批量启动任务完全成功: [12]
2025-07-29 09:03:38.685 | d89eb1fbfca24e54b4ac1be433baf035 | ERROR    | exceptions.handle:exception_handler:117 - 数据库连接错误: (asyncmy.errors.OperationalError) (2014, 'Command Out of Sync')
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-07-29 09:03:43.339 | 5e74cb5f6b3a457b87241b396b295672 | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 09:03:43.339 | 5e74cb5f6b3a457b87241b396b295672 | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 09:03:43.339 | 5e74cb5f6b3a457b87241b396b295672 | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-29 09:03:43.340 | 5e74cb5f6b3a457b87241b396b295672 | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 09:03:43.340 | 5e74cb5f6b3a457b87241b396b295672 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 09:03:43.340 | 5e74cb5f6b3a457b87241b396b295672 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 09:03:43.340 | 5e74cb5f6b3a457b87241b396b295672 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=100
2025-07-29 09:03:43.340 | 5e74cb5f6b3a457b87241b396b295672 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 09:03:43.341 | 5e74cb5f6b3a457b87241b396b295672 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 09:03:43.344 | 5e74cb5f6b3a457b87241b396b295672 | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 09:03:43.345 | 5e74cb5f6b3a457b87241b396b295672 | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 09:03:43.345 | 5e74cb5f6b3a457b87241b396b295672 | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 09:03:43.345 | 5e74cb5f6b3a457b87241b396b295672 | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 09:03:43.346 | 5e74cb5f6b3a457b87241b396b295672 | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆检测
2025-07-29 09:03:43.346 | 5e74cb5f6b3a457b87241b396b295672 | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 09:03:43.346 | 5e74cb5f6b3a457b87241b396b295672 | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 09:03:43.346 | 5e74cb5f6b3a457b87241b396b295672 | INFO     | module_stream.controller.monitor_controller:get_monitor_tasks:53 - 获取监控任务列表成功
2025-07-29 09:03:43.351 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:52 - 开始WebSocket连接: 任务12 (无认证模式)
2025-07-29 09:03:43.352 |  | INFO     | module_stream.service.task_execution_service:add_monitor_client:4177 - 客户端 c32e702d-536e-475c-9849-fd404174b429 已连接到任务 12 的监控流
2025-07-29 09:03:43.352 |  | INFO     | module_stream.controller.monitor_websocket_controller:connect_monitor_stream:73 - 客户端 c32e702d-536e-475c-9849-fd404174b429 连接到任务 12 的监控流 (无认证模式)
2025-07-29 09:04:18.394 | 09f5e4521de14c87b6fd492297da5c3e | INFO     | module_stream.service.task_execution_service:stop_task:306 - 开始停止任务: 12
2025-07-29 09:04:18.395 | 09f5e4521de14c87b6fd492297da5c3e | INFO     | module_stream.service.task_execution_service:stop_task:330 - 已从运行任务列表移除: 12
2025-07-29 09:04:18.395 | 09f5e4521de14c87b6fd492297da5c3e | INFO     | module_stream.service.task_execution_service:stop_task:339 - 任务 12 使用智驱力直接集成，无需清理外部进程
2025-07-29 09:04:18.395 | 09f5e4521de14c87b6fd492297da5c3e | INFO     | module_stream.service.task_execution_service:stop_monitor_stream:4118 - 任务 12 的监控流已停止
2025-07-29 09:04:18.396 | 09f5e4521de14c87b6fd492297da5c3e | INFO     | module_stream.service.task_execution_service:stop_task:344 - 监控流停止成功: 12
2025-07-29 09:04:18.396 | 09f5e4521de14c87b6fd492297da5c3e | INFO     | module_stream.service.task_execution_service:_clear_task_cache:133 - 任务12缓存已清除
2025-07-29 09:04:18.396 | 09f5e4521de14c87b6fd492297da5c3e | INFO     | module_stream.service.task_execution_service:stop_task:353 - 任务缓存清理成功: 12
2025-07-29 09:04:18.435 | 09f5e4521de14c87b6fd492297da5c3e | INFO     | module_stream.service.task_execution_service:stop_task:362 - 任务状态更新成功: 12
2025-07-29 09:04:18.435 | 09f5e4521de14c87b6fd492297da5c3e | INFO     | module_stream.service.task_execution_service:stop_task:374 - 任务 12 停止成功，包括实时监控流
2025-07-29 09:04:18.450 | 09f5e4521de14c87b6fd492297da5c3e | INFO     | module_stream.controller.monitor_controller:batch_stop_tasks:209 - 批量停止任务成功: [12]
2025-07-29 09:04:18.496 |  | INFO     | module_stream.controller.monitor_websocket_controller:_start_stream_push:180 - 客户端 c32e702d-536e-475c-9849-fd404174b429 主动断开连接
2025-07-29 09:04:18.496 |  | INFO     | module_stream.controller.monitor_websocket_controller:_cleanup_connection:209 - 客户端 c32e702d-536e-475c-9849-fd404174b429 连接已清理
2025-07-29 09:05:28.883 |  | INFO     | config.get_redis:close_redis_pool:55 - 关闭redis连接成功
2025-07-29 09:05:28.883 |  | INFO     | config.get_scheduler:close_system_scheduler:152 - 关闭定时任务成功
