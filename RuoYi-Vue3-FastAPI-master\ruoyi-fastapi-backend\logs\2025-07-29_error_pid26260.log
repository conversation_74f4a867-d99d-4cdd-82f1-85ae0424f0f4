2025-07-29 09:30:37.939 |  | INFO     | server:lifespan:45 - IntelligentSurveillanceAnalyticsSystem开始启动
2025-07-29 09:30:37.939 |  | INFO     | config.get_db:init_create_table:21 - 初始化数据库连接...
2025-07-29 09:30:37.973 |  | INFO     | config.get_db:init_create_table:24 - 数据库连接成功
2025-07-29 09:30:37.973 |  | INFO     | config.get_redis:create_redis_pool:22 - 开始连接redis...
2025-07-29 09:30:37.974 |  | INFO     | config.get_redis:create_redis_pool:35 - redis连接成功
2025-07-29 09:30:38.008 |  | INFO     | config.get_scheduler:init_system_scheduler:134 - 开始启动定时任务...
2025-07-29 09:30:38.022 |  | INFO     | config.get_scheduler:init_system_scheduler:142 - 系统初始定时任务加载成功
2025-07-29 09:30:38.022 |  | INFO     | server:lifespan:52 - IntelligentSurveillanceAnalyticsSystem启动成功
2025-07-29 09:30:39.627 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.controller.task_controller:get_task_list:50 - 接收到的查询参数 - taskName: None, status: None
2025-07-29 09:30:39.627 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.service.task_service:get_task_list_services:37 - === TaskService.get_task_list_services ===
2025-07-29 09:30:39.627 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.service.task_service:get_task_list_services:38 - 当前用户ID: 1
2025-07-29 09:30:39.628 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.service.task_service:get_task_list_services:39 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:30:39.628 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.service.task_service:get_task_list_services:40 - 是否分页: True
2025-07-29 09:30:39.628 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.dao.task_dao:get_task_list_by_user:144 - === 任务列表查询调试 ===
2025-07-29 09:30:39.628 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.dao.task_dao:get_task_list_by_user:145 - 用户ID: 1
2025-07-29 09:30:39.628 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.dao.task_dao:get_task_list_by_user:146 - 查询参数: task_id=None task_name=None stream_id=None algorithm_id=None algorithm_name=None algorithm_version=None algorithm_type=None user_config=None model_id=None config_id=None algorithm_config=None bbox_config=None alert_config=None schedule_config=None status=None last_run_time=None next_run_time=None run_count=None alert_count=None error_count=None del_flag=None create_by=None create_time=None update_by=None update_time=None remark=None page_num=1 page_size=10
2025-07-29 09:30:39.629 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.dao.task_dao:get_task_list_by_user:147 - 是否分页: True
2025-07-29 09:30:39.629 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.dao.task_dao:get_task_list_by_user:148 - 查询条件数量: 1
2025-07-29 09:30:39.634 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.dao.task_dao:get_task_list_by_user:159 - 分页查询结果: total=1, rows=1
2025-07-29 09:30:39.635 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.service.task_service:get_task_list_services:45 - DAO查询结果类型: <class 'utils.page_util.PageResponseModel'>
2025-07-29 09:30:39.635 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.service.task_service:get_task_list_services:47 - 分页结果: total=1, rows=1
2025-07-29 09:30:39.635 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.service.task_service:get_task_list_services:53 - 开始构建分页任务详情，任务数量: 1
2025-07-29 09:30:39.637 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.service.task_service:get_task_list_services:59 - 任务 1 详情构建成功: 车辆监控
2025-07-29 09:30:39.637 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.service.task_service:get_task_list_services:64 - 分页任务详情构建完成，最终数量: 1
2025-07-29 09:30:39.637 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.service.task_service:get_task_list_services:80 - === TaskService.get_task_list_services 完成 ===
2025-07-29 09:30:39.637 | 4c5ddae5cf0c464b8987bb6d2f8796ab | INFO     | module_stream.controller.task_controller:get_task_list:62 - 获取成功
